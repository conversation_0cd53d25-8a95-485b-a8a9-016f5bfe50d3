/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.otel.extensions.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import io.opentelemetry.sdk.autoconfigure.AutoConfiguredOpenTelemetrySdk;
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DynamicSanitizationRuleInstallerTest {

  @Mock private AutoConfiguredOpenTelemetrySdk mockSdk;

  private DynamicSanitizationRuleInstaller installer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    installer = new DynamicSanitizationRuleInstaller();

    // Clear any existing singleton instance and stop it if running
    DynamicSanitizationRuleManager existingManager = DynamicSanitizationRuleManager.getInstance();
    if (existingManager != null) {
      existingManager.stop();
    }
    DynamicSanitizationRuleManager.setInstance(null);
  }

  @AfterEach
  void tearDown() {
    // Clean up singleton instance
    DynamicSanitizationRuleManager ruleManager = DynamicSanitizationRuleManager.getInstance();
    if (ruleManager != null) {
      ruleManager.stop();
      DynamicSanitizationRuleManager.setInstance(null);
    }
  }

  @Test
  void testBeforeAgent_InitializesRuleManager() {
    // When
    installer.beforeAgent(mockSdk);

    // Then
    DynamicSanitizationRuleManager ruleManager = DynamicSanitizationRuleManager.getInstance();
    assertNotNull(ruleManager, "Rule manager should be initialized");

    // Verify it's the same instance returned by the installer
    assertEquals(ruleManager, DynamicSanitizationRuleInstaller.getRuleManager());
  }

  @Test
  void testBeforeAgent_HandlesExceptionGracefully() {
    // This test verifies that if initialization fails, it doesn't crash the agent
    // We can't easily simulate a failure in the constructor, but we can verify
    // that the method completes without throwing exceptions

    assertDoesNotThrow(() -> installer.beforeAgent(mockSdk));
  }

  @Test
  void testOrder_ReturnsCorrectValue() {
    // The installer should run after basic configs (order 0) but before filters
    assertEquals(5, installer.order());
  }

  @Test
  void testGetRuleManager_ConsistentWithSingleton() {
    // Test that both methods return the same instance (or both null)
    DynamicSanitizationRuleManager fromInstaller =
        DynamicSanitizationRuleInstaller.getRuleManager();
    DynamicSanitizationRuleManager fromSingleton = DynamicSanitizationRuleManager.getInstance();

    // Both should return the same instance (either both null or both the same object)
    assertEquals(fromInstaller, fromSingleton, "Both methods should return the same instance");
  }

  @Test
  void testGetRuleManager_ReturnsSameInstanceAfterInitialization() {
    // When
    installer.beforeAgent(mockSdk);

    // Then
    DynamicSanitizationRuleManager ruleManager1 = DynamicSanitizationRuleInstaller.getRuleManager();
    DynamicSanitizationRuleManager ruleManager2 = DynamicSanitizationRuleManager.getInstance();

    assertNotNull(ruleManager1);
    assertSame(ruleManager1, ruleManager2, "Both methods should return the same instance");
  }
}
