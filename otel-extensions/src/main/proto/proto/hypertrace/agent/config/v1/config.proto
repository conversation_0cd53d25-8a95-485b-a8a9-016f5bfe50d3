syntax = "proto3";

package hypertrace.agent.config.v1;

import "google/protobuf/wrappers.proto";

option go_package = "github.com/hypertrace/agent-config/gen/go/v1";
option java_package = "org.hypertrace.agent.config.v1";

// AgentConfig covers the config for agents.
// The config uses wrappers for primitive types to allow nullable values.
// The nullable values are used for instance to explicitly disable data capture or secure connection.
// Since the wrappers change structure of the objects the marshalling and unmarshalling
// have to be done via protobuf marshallers.
message AgentConfig {
    // service_name identifies the service/process running e.g. "my service"
    google.protobuf.StringValue service_name = 1;

    // reporting holds the reporting settings for the agent
    Reporting reporting = 2;

    // data_capture describes the data being captured by instrumentation
    DataCapture data_capture = 3;

    // propagation_formats list the supported propagation formats
    repeated PropagationFormat propagation_formats = 4;

    // when `false`, disables the agent
    google.protobuf.BoolValue enabled = 5;

    // javaagent has the configs specific to javaagent
    JavaAgent javaagent = 6;

    // resource_attributes map define the static list of resources which is configured on the tracer
    map<string, string> resource_attributes = 7;

    // telemetry
    Telemetry telemetry = 8;
}

// Reporting covers the options related to the mechanics for sending data to the
// tracing server o collector.
message Reporting {
    // endpoint represents the endpoint for reporting the traces
    // For ZIPKIN reporter type use http://api.traceable.ai:9411/api/v2/spans
    // For OTLP reporter type use http://api.traceable.ai:4317
    google.protobuf.StringValue endpoint = 1;

    // when `true`, connects to endpoints over TLS.
    google.protobuf.BoolValue secure = 2;

    // user specific token to access Traceable API
    google.protobuf.StringValue token = 3;

    // reporter type for traces.
    TraceReporterType trace_reporter_type = 5;

    // Certificate file containing the CA to verify the server's certificate.
    // This is for private certificates.
    // If this is set then `secure` above should also be set to `true`.
    google.protobuf.StringValue cert_file = 6;

    // metric_endpoint represents the endpoint for reporting the metrics.
    // For OTLP metric reporter type use http://api.traceable.ai:4317
    google.protobuf.StringValue metric_endpoint = 7;

    // reporter type for metrics.
    MetricReporterType metric_reporter_type = 8;

    // When `true`, modifies grpc resolver to use dns instead of passthrough and configure round robin client side loadbalancing
    google.protobuf.BoolValue enable_grpc_loadbalancing = 9;
}

// Message describes what message should be considered for certain DataCapture option
message Message {
    // when `false` it disables the capture for the request in a client/request operation
    google.protobuf.BoolValue request = 1;

    // when `false` it disables the capture for the response in a client/request operation
    google.protobuf.BoolValue response = 2;
}

// DataCapture describes the elements to be captured by the agent instrumentation
message DataCapture {
    // http_headers enables/disables the capture of the request/response headers in HTTP
    Message http_headers = 1;

    // http_body enables/disables the capture of the request/response body in HTTP
    Message http_body = 2;

    // rpc_metadata enables/disables the capture of the request/response metadata in RPC
    Message rpc_metadata = 3;

    // rpc_body enables/disables the capture of the request/response body in RPC
    Message rpc_body = 4;

    // kafka_body enables/disables the capture of the request/response message body in Kafka
    Message kafka_body = 7;

    // body_max_size_bytes is the maximum size of captured body in bytes.
    // Default should be 131_072 (128 KiB).
    google.protobuf.Int32Value body_max_size_bytes = 5;

    // body_max_processing_size_bytes is maximum size of body being processed by filters in bytes. 
    // Default should be 1_048_576 (1MB).
    //
    // For uncompressed bodies we capture all bytes up to `body_max_processing_size_bytes`
    // in memory and pass that through the filter.
    // For compressed and GRPC bodies, if the size of the body is larger than this, we ignore
    // it entirely, otherwise we decompress/decode the body and then pass it to the filter.
    google.protobuf.Int32Value body_max_processing_size_bytes = 6;

    // Array of allowed content type substrings to record
    // default should be json, x-www-form-urlencoded
    // ex: ["json"] will record any request bodies that have a content-type header that includes "json"
    repeated google.protobuf.StringValue allowed_content_types = 10;
}

// PropagationFormat represents the propagation formats supported by agents
enum PropagationFormat {
    // B3 propagation format, agents should support both multi and single value formats
    // see https://github.com/openzipkin/b3-propagation
    B3 = 0;

    // W3C Propagation format
    // see https://www.w3.org/TR/trace-context/
    TRACECONTEXT = 1;
}

// TraceReporterType represents the reporting format for trace data.
enum TraceReporterType {

    // Default to none. Agent will use it's default reporting type
    UNSPECIFIED = 0;

    // Zipkin protobuf reporting format.
    // see https://github.com/openzipkin/zipkin-api
    ZIPKIN = 1;

    // OpenTelemetry protobuf reporting format.
    // see https://github.com/open-telemetry/opentelemetry-proto
    OTLP = 2;

    // Logging reporting format
    LOGGING = 3;

    // Disable trace reporting
    NONE = 4;

    // OTLP over http
    OTLP_HTTP = 5;
}

// MetricReporterType represents the reporting format for metric data.
enum MetricReporterType {

    // Default to none. Agent will use it's default reporting type
    METRIC_REPORTER_TYPE_UNSPECIFIED = 0;

    // OpenTelemetry protobuf reporting format.
    // see https://github.com/open-telemetry/opentelemetry-proto
    METRIC_REPORTER_TYPE_OTLP = 1;

    // Prometheus exposition format.
    // see https://github.com/prometheus/docs/blob/main/content/docs/instrumenting/exposition_formats.md
    METRIC_REPORTER_TYPE_PROMETHEUS = 2;

    // Logging reporting format
    METRIC_REPORTER_TYPE_LOGGING = 3;

    // Disable metric reporting
    METRIC_REPORTER_TYPE_NONE = 4;
}

// JavaAgent has the configs specific to javaagent
message JavaAgent {
    // filter_jar_paths is the list of path to filter jars, separated by `,`
    repeated google.protobuf.StringValue filter_jar_paths = 1;
}

// Telemetry has config for agent telemetry: traces and metrics on agent's
// performance and events.
message Telemetry {
    // when `true`, an internal span is created and exported when the agent is initialized and started.
    // It's useful to denote when the application the agent is in started.
    google.protobuf.BoolValue startup_span_enabled = 1;

    // Whether to capture metrics or not. The metrics will be otel go metrics.
    // See https://github.com/open-telemetry/opentelemetry-go/tree/main/metric
    google.protobuf.BoolValue metrics_enabled = 2;
}
