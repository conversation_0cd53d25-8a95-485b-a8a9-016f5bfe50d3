// Code generated by github.com/hypertrace/agent-config/tools/go-generator. DO NOT EDIT.

package v1

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/ghodss/yaml"
	"google.golang.org/protobuf/encoding/protojson"
)

// getBoolEnv returns the bool value for an env var and a confirmation
// if the var exists
func getBoolEnv(name string) (bool, bool) {
	val := os.Getenv(name)
	switch val {
	case "true":
		return true, true
	case "false":
		return false, true
	default:
		return false, false
	}
}

// getStringEnv returns the string value for an env var and a confirmation
// if the var exists
func getStringEnv(name string) (string, bool) {
	if val := os.Getenv(name); val != "" {
		return val, true
	}

	return "", false
}

func getArrayStringEnv(name string) ([]string, bool) {
	if val := os.Getenv(name); val != "" {
		return strings.Split(val, ","), true
	}

	return nil, false
}

// getInt32Env returns the int32 value for an env var and a confirmation
// if the var exists
func getInt32Env(name string) (int32, bool) {
	if val := os.Getenv(name); val != "" {
		intVal, err := strconv.Atoi(val)
		return int32(intVal), err == nil
	}

	return 0, false
}

// loadFromFile loads the agent config from a file
func loadFromFile(c *AgentConfig, filename string) error {
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	fcontent, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", filename, err)
	}
	switch ext := filepath.Ext(filename); ext {
	case ".json":
		// The usage of wrappers for scalars in protos make it impossible to use standard
		// unmarshalers as the wrapped values aren't scalars but of type Message, hence they
		// have object structure in json e.g. myBoolVal: {Value: true} instead of myBoolVal:true
		// protojson is meant to solve this problem.
		return unmarshaler.Unmarshal(fcontent, c)
	case ".yaml", ".yml":
		// Because of the reson mentioned above we can't use YAML parsers either and hence
		// we convert the YAML into JSON in order to parse the JSON value with protojson.
		// The implications of this is that comments and multi-line strings aren't desirable.
		fcontentAsJSON, err := yaml.YAMLToJSON(fcontent)
		if err != nil {
			return fmt.Errorf("failed to parse file %s: %v", filename, err)
		}
		return unmarshaler.Unmarshal(fcontentAsJSON, c)
	default:
		return fmt.Errorf("unknown extension: %s", ext)
	}
}

// fileExists checks if a file exists
func fileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return !info.IsDir()
}

// Load loads the configuration from the default values, config file and env vars with a given prefix.
func Load(opts ...LoadOption) *AgentConfig {
	options := defaultOptions
	for _, opt := range opts {
		opt(&options)
	}

	cfg := AgentConfig{}

	if configFile := os.Getenv(options.prefix + "CONFIG_FILE"); configFile != "" {
		absConfigFile, err := filepath.Abs(configFile)
		if err != nil {
			log.Printf("failed to resolve absolute path for %q: %v.\n", configFile, err)
		}

		if fileExists(absConfigFile) {
			if err := loadFromFile(&cfg, absConfigFile); err != nil {
				log.Printf("failed to load the config from %q: %v\n", absConfigFile, err)
			}
		} else {
			log.Printf("config file %q does not exist.\n", absConfigFile)
		}
	}

	cfg.loadFromEnv(options.prefix, options.defaultConfig)

	return &cfg
}

// LoadFromFile loads the configuration from the default values, config file and env vars.
func LoadFromFile(configFile string, opts ...LoadOption) *AgentConfig {
	options := defaultOptions
	for _, opt := range opts {
		opt(&options)
	}

	cfg := AgentConfig{}

	absConfigFile, err := filepath.Abs(configFile)
	if err != nil {
		log.Printf("failed to resolve absolute path for %q: %v.\n", configFile, err)
	}

	if fileExists(absConfigFile) {
		if err := loadFromFile(&cfg, absConfigFile); err != nil {
			log.Printf("failed to load the config from %q: %v\n", absConfigFile, err)
		}
	} else {
		log.Printf("config file %q does not exist.\n", absConfigFile)
	}

	cfg.loadFromEnv(options.prefix, options.defaultConfig)

	return &cfg
}
