// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.13.0
// source: hypertrace/agent/config/v1/config.proto

package v1

import (
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PropagationFormat represents the propagation formats supported by agents
type PropagationFormat int32

const (
	// B3 propagation format, agents should support both multi and single value formats
	// see https://github.com/openzipkin/b3-propagation
	PropagationFormat_B3 PropagationFormat = 0
	// W3C Propagation format
	// see https://www.w3.org/TR/trace-context/
	PropagationFormat_TRACECONTEXT PropagationFormat = 1
)

// Enum value maps for PropagationFormat.
var (
	PropagationFormat_name = map[int32]string{
		0: "B3",
		1: "TRACECONTEXT",
	}
	PropagationFormat_value = map[string]int32{
		"B3":           0,
		"TRACECONTEXT": 1,
	}
)

func (x PropagationFormat) Enum() *PropagationFormat {
	p := new(PropagationFormat)
	*p = x
	return p
}

func (x PropagationFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropagationFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_hypertrace_agent_config_v1_config_proto_enumTypes[0].Descriptor()
}

func (PropagationFormat) Type() protoreflect.EnumType {
	return &file_hypertrace_agent_config_v1_config_proto_enumTypes[0]
}

func (x PropagationFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropagationFormat.Descriptor instead.
func (PropagationFormat) EnumDescriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{0}
}

// TraceReporterType represents the reporting format for trace data.
type TraceReporterType int32

const (
	// Default to none. Agent will use it's default reporting type
	TraceReporterType_UNSPECIFIED TraceReporterType = 0
	// Zipkin protobuf reporting format.
	// see https://github.com/openzipkin/zipkin-api
	TraceReporterType_ZIPKIN TraceReporterType = 1
	// OpenTelemetry protobuf reporting format.
	// see https://github.com/open-telemetry/opentelemetry-proto
	TraceReporterType_OTLP TraceReporterType = 2
	// Logging reporting format
	TraceReporterType_LOGGING TraceReporterType = 3
	// Disable trace reporting
	TraceReporterType_NONE TraceReporterType = 4
	// OTLP over http
	TraceReporterType_OTLP_HTTP TraceReporterType = 5
)

// Enum value maps for TraceReporterType.
var (
	TraceReporterType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "ZIPKIN",
		2: "OTLP",
		3: "LOGGING",
		4: "NONE",
		5: "OTLP_HTTP",
	}
	TraceReporterType_value = map[string]int32{
		"UNSPECIFIED": 0,
		"ZIPKIN":      1,
		"OTLP":        2,
		"LOGGING":     3,
		"NONE":        4,
		"OTLP_HTTP":   5,
	}
)

func (x TraceReporterType) Enum() *TraceReporterType {
	p := new(TraceReporterType)
	*p = x
	return p
}

func (x TraceReporterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TraceReporterType) Descriptor() protoreflect.EnumDescriptor {
	return file_hypertrace_agent_config_v1_config_proto_enumTypes[1].Descriptor()
}

func (TraceReporterType) Type() protoreflect.EnumType {
	return &file_hypertrace_agent_config_v1_config_proto_enumTypes[1]
}

func (x TraceReporterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TraceReporterType.Descriptor instead.
func (TraceReporterType) EnumDescriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{1}
}

// MetricReporterType represents the reporting format for metric data.
type MetricReporterType int32

const (
	// Default to none. Agent will use it's default reporting type
	MetricReporterType_METRIC_REPORTER_TYPE_UNSPECIFIED MetricReporterType = 0
	// OpenTelemetry protobuf reporting format.
	// see https://github.com/open-telemetry/opentelemetry-proto
	MetricReporterType_METRIC_REPORTER_TYPE_OTLP MetricReporterType = 1
	// Prometheus exposition format.
	// see https://github.com/prometheus/docs/blob/main/content/docs/instrumenting/exposition_formats.md
	MetricReporterType_METRIC_REPORTER_TYPE_PROMETHEUS MetricReporterType = 2
	// Logging reporting format
	MetricReporterType_METRIC_REPORTER_TYPE_LOGGING MetricReporterType = 3
	// Disable metric reporting
	MetricReporterType_METRIC_REPORTER_TYPE_NONE MetricReporterType = 4
)

// Enum value maps for MetricReporterType.
var (
	MetricReporterType_name = map[int32]string{
		0: "METRIC_REPORTER_TYPE_UNSPECIFIED",
		1: "METRIC_REPORTER_TYPE_OTLP",
		2: "METRIC_REPORTER_TYPE_PROMETHEUS",
		3: "METRIC_REPORTER_TYPE_LOGGING",
		4: "METRIC_REPORTER_TYPE_NONE",
	}
	MetricReporterType_value = map[string]int32{
		"METRIC_REPORTER_TYPE_UNSPECIFIED": 0,
		"METRIC_REPORTER_TYPE_OTLP":        1,
		"METRIC_REPORTER_TYPE_PROMETHEUS":  2,
		"METRIC_REPORTER_TYPE_LOGGING":     3,
		"METRIC_REPORTER_TYPE_NONE":        4,
	}
)

func (x MetricReporterType) Enum() *MetricReporterType {
	p := new(MetricReporterType)
	*p = x
	return p
}

func (x MetricReporterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MetricReporterType) Descriptor() protoreflect.EnumDescriptor {
	return file_hypertrace_agent_config_v1_config_proto_enumTypes[2].Descriptor()
}

func (MetricReporterType) Type() protoreflect.EnumType {
	return &file_hypertrace_agent_config_v1_config_proto_enumTypes[2]
}

func (x MetricReporterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MetricReporterType.Descriptor instead.
func (MetricReporterType) EnumDescriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{2}
}

// AgentConfig covers the config for agents.
// The config uses wrappers for primitive types to allow nullable values.
// The nullable values are used for instance to explicitly disable data capture or secure connection.
// Since the wrappers change structure of the objects the marshalling and unmarshalling
// have to be done via protobuf marshallers.
type AgentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_name identifies the service/process running e.g. "my service"
	ServiceName *wrappers.StringValue `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// reporting holds the reporting settings for the agent
	Reporting *Reporting `protobuf:"bytes,2,opt,name=reporting,proto3" json:"reporting,omitempty"`
	// data_capture describes the data being captured by instrumentation
	DataCapture *DataCapture `protobuf:"bytes,3,opt,name=data_capture,json=dataCapture,proto3" json:"data_capture,omitempty"`
	// propagation_formats list the supported propagation formats
	PropagationFormats []PropagationFormat `protobuf:"varint,4,rep,packed,name=propagation_formats,json=propagationFormats,proto3,enum=hypertrace.agent.config.v1.PropagationFormat" json:"propagation_formats,omitempty"`
	// when `false`, disables the agent
	Enabled *wrappers.BoolValue `protobuf:"bytes,5,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// javaagent has the configs specific to javaagent
	Javaagent *JavaAgent `protobuf:"bytes,6,opt,name=javaagent,proto3" json:"javaagent,omitempty"`
	// resource_attributes map define the static list of resources which is configured on the tracer
	ResourceAttributes map[string]string `protobuf:"bytes,7,rep,name=resource_attributes,json=resourceAttributes,proto3" json:"resource_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// telemetry
	Telemetry *Telemetry `protobuf:"bytes,8,opt,name=telemetry,proto3" json:"telemetry,omitempty"`
}

func (x *AgentConfig) Reset() {
	*x = AgentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentConfig) ProtoMessage() {}

func (x *AgentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentConfig.ProtoReflect.Descriptor instead.
func (*AgentConfig) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{0}
}

func (x *AgentConfig) GetServiceName() *wrappers.StringValue {
	if x != nil {
		return x.ServiceName
	}
	return nil
}

func (x *AgentConfig) GetReporting() *Reporting {
	if x != nil {
		return x.Reporting
	}
	return nil
}

func (x *AgentConfig) GetDataCapture() *DataCapture {
	if x != nil {
		return x.DataCapture
	}
	return nil
}

func (x *AgentConfig) GetPropagationFormats() []PropagationFormat {
	if x != nil {
		return x.PropagationFormats
	}
	return nil
}

func (x *AgentConfig) GetEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.Enabled
	}
	return nil
}

func (x *AgentConfig) GetJavaagent() *JavaAgent {
	if x != nil {
		return x.Javaagent
	}
	return nil
}

func (x *AgentConfig) GetResourceAttributes() map[string]string {
	if x != nil {
		return x.ResourceAttributes
	}
	return nil
}

func (x *AgentConfig) GetTelemetry() *Telemetry {
	if x != nil {
		return x.Telemetry
	}
	return nil
}

// Reporting covers the options related to the mechanics for sending data to the
// tracing server o collector.
type Reporting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// endpoint represents the endpoint for reporting the traces
	// For ZIPKIN reporter type use http://api.traceable.ai:9411/api/v2/spans
	// For OTLP reporter type use http://api.traceable.ai:4317
	Endpoint *wrappers.StringValue `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	// when `true`, connects to endpoints over TLS.
	Secure *wrappers.BoolValue `protobuf:"bytes,2,opt,name=secure,proto3" json:"secure,omitempty"`
	// user specific token to access Traceable API
	Token *wrappers.StringValue `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// reporter type for traces.
	TraceReporterType TraceReporterType `protobuf:"varint,5,opt,name=trace_reporter_type,json=traceReporterType,proto3,enum=hypertrace.agent.config.v1.TraceReporterType" json:"trace_reporter_type,omitempty"`
	// Certificate file containing the CA to verify the server's certificate.
	// This is for private certificates.
	// If this is set then `secure` above should also be set to `true`.
	CertFile *wrappers.StringValue `protobuf:"bytes,6,opt,name=cert_file,json=certFile,proto3" json:"cert_file,omitempty"`
	// metric_endpoint represents the endpoint for reporting the metrics.
	// For OTLP metric reporter type use http://api.traceable.ai:4317
	MetricEndpoint *wrappers.StringValue `protobuf:"bytes,7,opt,name=metric_endpoint,json=metricEndpoint,proto3" json:"metric_endpoint,omitempty"`
	// reporter type for metrics.
	MetricReporterType MetricReporterType `protobuf:"varint,8,opt,name=metric_reporter_type,json=metricReporterType,proto3,enum=hypertrace.agent.config.v1.MetricReporterType" json:"metric_reporter_type,omitempty"`
	// When `true`, modifies grpc resolver to use dns instead of passthrough and configure round robin client side loadbalancing
	EnableGrpcLoadbalancing *wrappers.BoolValue `protobuf:"bytes,9,opt,name=enable_grpc_loadbalancing,json=enableGrpcLoadbalancing,proto3" json:"enable_grpc_loadbalancing,omitempty"`
}

func (x *Reporting) Reset() {
	*x = Reporting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reporting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reporting) ProtoMessage() {}

func (x *Reporting) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reporting.ProtoReflect.Descriptor instead.
func (*Reporting) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{1}
}

func (x *Reporting) GetEndpoint() *wrappers.StringValue {
	if x != nil {
		return x.Endpoint
	}
	return nil
}

func (x *Reporting) GetSecure() *wrappers.BoolValue {
	if x != nil {
		return x.Secure
	}
	return nil
}

func (x *Reporting) GetToken() *wrappers.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *Reporting) GetTraceReporterType() TraceReporterType {
	if x != nil {
		return x.TraceReporterType
	}
	return TraceReporterType_UNSPECIFIED
}

func (x *Reporting) GetCertFile() *wrappers.StringValue {
	if x != nil {
		return x.CertFile
	}
	return nil
}

func (x *Reporting) GetMetricEndpoint() *wrappers.StringValue {
	if x != nil {
		return x.MetricEndpoint
	}
	return nil
}

func (x *Reporting) GetMetricReporterType() MetricReporterType {
	if x != nil {
		return x.MetricReporterType
	}
	return MetricReporterType_METRIC_REPORTER_TYPE_UNSPECIFIED
}

func (x *Reporting) GetEnableGrpcLoadbalancing() *wrappers.BoolValue {
	if x != nil {
		return x.EnableGrpcLoadbalancing
	}
	return nil
}

// Message describes what message should be considered for certain DataCapture option
type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// when `false` it disables the capture for the request in a client/request operation
	Request *wrappers.BoolValue `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	// when `false` it disables the capture for the response in a client/request operation
	Response *wrappers.BoolValue `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{2}
}

func (x *Message) GetRequest() *wrappers.BoolValue {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *Message) GetResponse() *wrappers.BoolValue {
	if x != nil {
		return x.Response
	}
	return nil
}

// DataCapture describes the elements to be captured by the agent instrumentation
type DataCapture struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// http_headers enables/disables the capture of the request/response headers in HTTP
	HttpHeaders *Message `protobuf:"bytes,1,opt,name=http_headers,json=httpHeaders,proto3" json:"http_headers,omitempty"`
	// http_body enables/disables the capture of the request/response body in HTTP
	HttpBody *Message `protobuf:"bytes,2,opt,name=http_body,json=httpBody,proto3" json:"http_body,omitempty"`
	// rpc_metadata enables/disables the capture of the request/response metadata in RPC
	RpcMetadata *Message `protobuf:"bytes,3,opt,name=rpc_metadata,json=rpcMetadata,proto3" json:"rpc_metadata,omitempty"`
	// rpc_body enables/disables the capture of the request/response body in RPC
	RpcBody *Message `protobuf:"bytes,4,opt,name=rpc_body,json=rpcBody,proto3" json:"rpc_body,omitempty"`
	// body_max_size_bytes is the maximum size of captured body in bytes.
	// Default should be 131_072 (128 KiB).
	BodyMaxSizeBytes *wrappers.Int32Value `protobuf:"bytes,5,opt,name=body_max_size_bytes,json=bodyMaxSizeBytes,proto3" json:"body_max_size_bytes,omitempty"`
	// body_max_processing_size_bytes is maximum size of body being processed by filters in bytes.
	// Default should be 1_048_576 (1MB).
	//
	// For uncompressed bodies we capture all bytes up to `body_max_processing_size_bytes`
	// in memory and pass that through the filter.
	// For compressed and GRPC bodies, if the size of the body is larger than this, we ignore
	// it entirely, otherwise we decompress/decode the body and then pass it to the filter.
	BodyMaxProcessingSizeBytes *wrappers.Int32Value `protobuf:"bytes,6,opt,name=body_max_processing_size_bytes,json=bodyMaxProcessingSizeBytes,proto3" json:"body_max_processing_size_bytes,omitempty"`
	// Array of allowed content type substrings to record
	// default should be json, x-www-form-urlencoded
	// ex: ["json"] will record any request bodies that have a content-type header that includes "json"
	AllowedContentTypes []*wrappers.StringValue `protobuf:"bytes,10,rep,name=allowed_content_types,json=allowedContentTypes,proto3" json:"allowed_content_types,omitempty"`
}

func (x *DataCapture) Reset() {
	*x = DataCapture{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataCapture) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataCapture) ProtoMessage() {}

func (x *DataCapture) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataCapture.ProtoReflect.Descriptor instead.
func (*DataCapture) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{3}
}

func (x *DataCapture) GetHttpHeaders() *Message {
	if x != nil {
		return x.HttpHeaders
	}
	return nil
}

func (x *DataCapture) GetHttpBody() *Message {
	if x != nil {
		return x.HttpBody
	}
	return nil
}

func (x *DataCapture) GetRpcMetadata() *Message {
	if x != nil {
		return x.RpcMetadata
	}
	return nil
}

func (x *DataCapture) GetRpcBody() *Message {
	if x != nil {
		return x.RpcBody
	}
	return nil
}

func (x *DataCapture) GetBodyMaxSizeBytes() *wrappers.Int32Value {
	if x != nil {
		return x.BodyMaxSizeBytes
	}
	return nil
}

func (x *DataCapture) GetBodyMaxProcessingSizeBytes() *wrappers.Int32Value {
	if x != nil {
		return x.BodyMaxProcessingSizeBytes
	}
	return nil
}

func (x *DataCapture) GetAllowedContentTypes() []*wrappers.StringValue {
	if x != nil {
		return x.AllowedContentTypes
	}
	return nil
}

// JavaAgent has the configs specific to javaagent
type JavaAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter_jar_paths is the list of path to filter jars, separated by `,`
	FilterJarPaths []*wrappers.StringValue `protobuf:"bytes,1,rep,name=filter_jar_paths,json=filterJarPaths,proto3" json:"filter_jar_paths,omitempty"`
}

func (x *JavaAgent) Reset() {
	*x = JavaAgent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JavaAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JavaAgent) ProtoMessage() {}

func (x *JavaAgent) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JavaAgent.ProtoReflect.Descriptor instead.
func (*JavaAgent) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{4}
}

func (x *JavaAgent) GetFilterJarPaths() []*wrappers.StringValue {
	if x != nil {
		return x.FilterJarPaths
	}
	return nil
}

// Telemetry has config for agent telemetry: traces and metrics on agent's
// performance and events.
type Telemetry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// when `true`, an internal span is created and exported when the agent is initialized and started.
	// It's useful to denote when the application the agent is in started.
	StartupSpanEnabled *wrappers.BoolValue `protobuf:"bytes,1,opt,name=startup_span_enabled,json=startupSpanEnabled,proto3" json:"startup_span_enabled,omitempty"`
	// Whether to capture metrics or not. The metrics will be otel go metrics.
	// See https://github.com/open-telemetry/opentelemetry-go/tree/main/metric
	MetricsEnabled *wrappers.BoolValue `protobuf:"bytes,2,opt,name=metrics_enabled,json=metricsEnabled,proto3" json:"metrics_enabled,omitempty"`
}

func (x *Telemetry) Reset() {
	*x = Telemetry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Telemetry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Telemetry) ProtoMessage() {}

func (x *Telemetry) ProtoReflect() protoreflect.Message {
	mi := &file_hypertrace_agent_config_v1_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Telemetry.ProtoReflect.Descriptor instead.
func (*Telemetry) Descriptor() ([]byte, []int) {
	return file_hypertrace_agent_config_v1_config_proto_rawDescGZIP(), []int{5}
}

func (x *Telemetry) GetStartupSpanEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.StartupSpanEnabled
	}
	return nil
}

func (x *Telemetry) GetMetricsEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.MetricsEnabled
	}
	return nil
}

var File_hypertrace_agent_config_v1_config_proto protoreflect.FileDescriptor

var file_hypertrace_agent_config_v1_config_proto_rawDesc = []byte{
	0x0a, 0x27, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x68, 0x79, 0x70, 0x65, 0x72,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x05, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x68, 0x79, 0x70, 0x65,
	0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x61,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x70, 0x61,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x61, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x70, 0x61, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x43, 0x0a,
	0x09, 0x6a, 0x61, 0x76, 0x61, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x61,
	0x76, 0x61, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x6a, 0x61, 0x76, 0x61, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x12, 0x70, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3f, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x09, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x1a, 0x45, 0x0a, 0x17, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xc8, 0x04, 0x0a, 0x09, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x38,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x32, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x5d, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x09, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x08, 0x63, 0x65, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x45, 0x0a, 0x0f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x12, 0x60, 0x0a, 0x14, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x12, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x19, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x67, 0x72,
	0x70, 0x63, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x70, 0x63, 0x4c, 0x6f,
	0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x22, 0x77, 0x0a, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9e, 0x04, 0x0a, 0x0b, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x79, 0x70,
	0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x0b, 0x68, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x09,
	0x68, 0x74, 0x74, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x68, 0x74, 0x74, 0x70, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x46,
	0x0a, 0x0c, 0x72, 0x70, 0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x72, 0x70, 0x63, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x08, 0x72, 0x70, 0x63, 0x5f, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x72,
	0x70, 0x63, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x4a, 0x0a, 0x13, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x6d,
	0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x10, 0x62, 0x6f, 0x64, 0x79, 0x4d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x5f, 0x0a, 0x1e, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x1a, 0x62, 0x6f, 0x64, 0x79, 0x4d, 0x61, 0x78,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x53, 0x0a, 0x09, 0x4a, 0x61, 0x76, 0x61, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6a, 0x61, 0x72,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x4a, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x09, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x75, 0x70, 0x5f, 0x73, 0x70, 0x61, 0x6e, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x53, 0x70, 0x61, 0x6e, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x2a, 0x2d, 0x0a, 0x11, 0x50,
	0x72, 0x6f, 0x70, 0x61, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x12, 0x06, 0x0a, 0x02, 0x42, 0x33, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x52, 0x41, 0x43,
	0x45, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x2a, 0x60, 0x0a, 0x11, 0x54, 0x72,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x5a, 0x49, 0x50, 0x4b, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04,
	0x4f, 0x54, 0x4c, 0x50, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x0d, 0x0a,
	0x09, 0x4f, 0x54, 0x4c, 0x50, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x05, 0x2a, 0xbf, 0x01, 0x0a,
	0x12, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x45, 0x54,
	0x52, 0x49, 0x43, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4f, 0x54, 0x4c, 0x50, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x45, 0x54, 0x52,
	0x49, 0x43, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x45, 0x54, 0x48, 0x45, 0x55, 0x53, 0x10, 0x02, 0x12, 0x20, 0x0a,
	0x1c, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12,
	0x1d, 0x0a, 0x19, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x42, 0x4e,
	0x0a, 0x1e, 0x6f, 0x72, 0x67, 0x2e, 0x68, 0x79, 0x70, 0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31,
	0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x79, 0x70,
	0x65, 0x72, 0x74, 0x72, 0x61, 0x63, 0x65, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2d, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x67, 0x6f, 0x2f, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hypertrace_agent_config_v1_config_proto_rawDescOnce sync.Once
	file_hypertrace_agent_config_v1_config_proto_rawDescData = file_hypertrace_agent_config_v1_config_proto_rawDesc
)

func file_hypertrace_agent_config_v1_config_proto_rawDescGZIP() []byte {
	file_hypertrace_agent_config_v1_config_proto_rawDescOnce.Do(func() {
		file_hypertrace_agent_config_v1_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_hypertrace_agent_config_v1_config_proto_rawDescData)
	})
	return file_hypertrace_agent_config_v1_config_proto_rawDescData
}

var file_hypertrace_agent_config_v1_config_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_hypertrace_agent_config_v1_config_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_hypertrace_agent_config_v1_config_proto_goTypes = []interface{}{
	(PropagationFormat)(0),       // 0: hypertrace.agent.config.v1.PropagationFormat
	(TraceReporterType)(0),       // 1: hypertrace.agent.config.v1.TraceReporterType
	(MetricReporterType)(0),      // 2: hypertrace.agent.config.v1.MetricReporterType
	(*AgentConfig)(nil),          // 3: hypertrace.agent.config.v1.AgentConfig
	(*Reporting)(nil),            // 4: hypertrace.agent.config.v1.Reporting
	(*Message)(nil),              // 5: hypertrace.agent.config.v1.Message
	(*DataCapture)(nil),          // 6: hypertrace.agent.config.v1.DataCapture
	(*JavaAgent)(nil),            // 7: hypertrace.agent.config.v1.JavaAgent
	(*Telemetry)(nil),            // 8: hypertrace.agent.config.v1.Telemetry
	nil,                          // 9: hypertrace.agent.config.v1.AgentConfig.ResourceAttributesEntry
	(*wrappers.StringValue)(nil), // 10: google.protobuf.StringValue
	(*wrappers.BoolValue)(nil),   // 11: google.protobuf.BoolValue
	(*wrappers.Int32Value)(nil),  // 12: google.protobuf.Int32Value
}
var file_hypertrace_agent_config_v1_config_proto_depIdxs = []int32{
	10, // 0: hypertrace.agent.config.v1.AgentConfig.service_name:type_name -> google.protobuf.StringValue
	4,  // 1: hypertrace.agent.config.v1.AgentConfig.reporting:type_name -> hypertrace.agent.config.v1.Reporting
	6,  // 2: hypertrace.agent.config.v1.AgentConfig.data_capture:type_name -> hypertrace.agent.config.v1.DataCapture
	0,  // 3: hypertrace.agent.config.v1.AgentConfig.propagation_formats:type_name -> hypertrace.agent.config.v1.PropagationFormat
	11, // 4: hypertrace.agent.config.v1.AgentConfig.enabled:type_name -> google.protobuf.BoolValue
	7,  // 5: hypertrace.agent.config.v1.AgentConfig.javaagent:type_name -> hypertrace.agent.config.v1.JavaAgent
	9,  // 6: hypertrace.agent.config.v1.AgentConfig.resource_attributes:type_name -> hypertrace.agent.config.v1.AgentConfig.ResourceAttributesEntry
	8,  // 7: hypertrace.agent.config.v1.AgentConfig.telemetry:type_name -> hypertrace.agent.config.v1.Telemetry
	10, // 8: hypertrace.agent.config.v1.Reporting.endpoint:type_name -> google.protobuf.StringValue
	11, // 9: hypertrace.agent.config.v1.Reporting.secure:type_name -> google.protobuf.BoolValue
	10, // 10: hypertrace.agent.config.v1.Reporting.token:type_name -> google.protobuf.StringValue
	1,  // 11: hypertrace.agent.config.v1.Reporting.trace_reporter_type:type_name -> hypertrace.agent.config.v1.TraceReporterType
	10, // 12: hypertrace.agent.config.v1.Reporting.cert_file:type_name -> google.protobuf.StringValue
	10, // 13: hypertrace.agent.config.v1.Reporting.metric_endpoint:type_name -> google.protobuf.StringValue
	2,  // 14: hypertrace.agent.config.v1.Reporting.metric_reporter_type:type_name -> hypertrace.agent.config.v1.MetricReporterType
	11, // 15: hypertrace.agent.config.v1.Reporting.enable_grpc_loadbalancing:type_name -> google.protobuf.BoolValue
	11, // 16: hypertrace.agent.config.v1.Message.request:type_name -> google.protobuf.BoolValue
	11, // 17: hypertrace.agent.config.v1.Message.response:type_name -> google.protobuf.BoolValue
	5,  // 18: hypertrace.agent.config.v1.DataCapture.http_headers:type_name -> hypertrace.agent.config.v1.Message
	5,  // 19: hypertrace.agent.config.v1.DataCapture.http_body:type_name -> hypertrace.agent.config.v1.Message
	5,  // 20: hypertrace.agent.config.v1.DataCapture.rpc_metadata:type_name -> hypertrace.agent.config.v1.Message
	5,  // 21: hypertrace.agent.config.v1.DataCapture.rpc_body:type_name -> hypertrace.agent.config.v1.Message
	12, // 22: hypertrace.agent.config.v1.DataCapture.body_max_size_bytes:type_name -> google.protobuf.Int32Value
	12, // 23: hypertrace.agent.config.v1.DataCapture.body_max_processing_size_bytes:type_name -> google.protobuf.Int32Value
	10, // 24: hypertrace.agent.config.v1.DataCapture.allowed_content_types:type_name -> google.protobuf.StringValue
	10, // 25: hypertrace.agent.config.v1.JavaAgent.filter_jar_paths:type_name -> google.protobuf.StringValue
	11, // 26: hypertrace.agent.config.v1.Telemetry.startup_span_enabled:type_name -> google.protobuf.BoolValue
	11, // 27: hypertrace.agent.config.v1.Telemetry.metrics_enabled:type_name -> google.protobuf.BoolValue
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_hypertrace_agent_config_v1_config_proto_init() }
func file_hypertrace_agent_config_v1_config_proto_init() {
	if File_hypertrace_agent_config_v1_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hypertrace_agent_config_v1_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hypertrace_agent_config_v1_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reporting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hypertrace_agent_config_v1_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hypertrace_agent_config_v1_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataCapture); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hypertrace_agent_config_v1_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JavaAgent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hypertrace_agent_config_v1_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Telemetry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hypertrace_agent_config_v1_config_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_hypertrace_agent_config_v1_config_proto_goTypes,
		DependencyIndexes: file_hypertrace_agent_config_v1_config_proto_depIdxs,
		EnumInfos:         file_hypertrace_agent_config_v1_config_proto_enumTypes,
		MessageInfos:      file_hypertrace_agent_config_v1_config_proto_msgTypes,
	}.Build()
	File_hypertrace_agent_config_v1_config_proto = out.File
	file_hypertrace_agent_config_v1_config_proto_rawDesc = nil
	file_hypertrace_agent_config_v1_config_proto_goTypes = nil
	file_hypertrace_agent_config_v1_config_proto_depIdxs = nil
}
