package main

import (
	"errors"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/iancoleman/strcase"
	"github.com/tallstoat/pbparser"
)

const filepathSeparator = string(filepath.Separator)

// protobufImportModuleProvider allows to load the wrappers from protobuf library
type protobufImportModuleProvider struct {
	dir string
}

func (pi *protobufImportModuleProvider) Provide(module string) (io.Reader, error) {
	modulePath := pi.dir + filepathSeparator + module
	if strings.HasPrefix(module, "google/protobuf/") {
		modulePath = pi.dir + filepathSeparator + "protobuf" + filepathSeparator + "src" + filepathSeparator + module
	}

	raw, err := ioutil.ReadFile(modulePath)
	if err != nil {
		return nil, err
	}

	r := strings.NewReader(string(raw[:]))
	return r, nil
}

func main() {
	var outFile = flag.String("o", "./ENV_VARS.md", "OUT_FILE for the generated code.")
	var prefix = flag.String("p", "HT_", "Prefix for the env vars.")
	flag.Parse()

	if len(flag.Args()) == 0 {
		fmt.Println(`Usage: env-vars-generator PROTO_FILE
Parse PROTO_FILE and generate output document`)
		return
	}

	filename := flag.Arg(0)
	f, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Unable to open the proto file %q: %v", filename, err)
		os.Exit(1)
	}

	path, err := os.Getwd()
	if err != nil {
		fmt.Printf("Unable to get current working directory: %v", err)
		os.Exit(1)
	}

	pf, err := pbparser.Parse(f, &protobufImportModuleProvider{
		path + filepathSeparator + "tools" + filepathSeparator + "env-vars-generator",
	})
	if err != nil {
		fmt.Printf("Unable to parse proto file %q: %v \n", filename, err)
		os.Exit(1)
	}

	w, err := os.Create(*outFile)
	if err != nil {
		fmt.Printf("Failed to open destination %q: %v \n", *outFile, err)
		os.Exit(1)
	}
	defer w.Close()

	_, err = w.WriteString("[//]: # (Code generated by hypertrace/agent-config/tools/env-vars-generator. DO NOT EDIT.)\n\n")
	if err != nil {
		fmt.Printf("File to write output %v\n", err)
		os.Exit(1)
	}

	header, err := readTemplate(path + "/tools/env-vars-generator/HEADER.tpl.md")
	if err != nil {
		fmt.Printf("File to read the header %v\n", err)
		os.Exit(1)
	}

	_, err = w.Write(header)
	if err != nil {
		fmt.Printf("File to write output %v\n", err)
		os.Exit(1)
	}

	allTypes, rootTypes, enumTypes := getTypes(pf)

	for _, _type := range rootTypes {
		err = writeHeaders(w)
		if err != nil {
			fmt.Printf("File to write headers %v \n", err)
			os.Exit(1)
		}
		err = writeRows(w, _type, allTypes, enumTypes, *prefix)
		if err != nil {
			fmt.Printf("File to write rows %v \n", err)
			os.Exit(1)
		}
	}
}

// readTemplate reads a template file skipping the comments
func readTemplate(filepath string) ([]byte, error) {
	hf, err := os.Open(filepath)
	if err != nil {
		return nil, err
	}
	defer hf.Close()

	template, err := ioutil.ReadAll(hf)
	if err != nil {
		return nil, err
	}

	for {
		commentIdx := strings.Index(string(template), "[//]: #")
		if commentIdx == -1 {
			break
		}

		newLineIdex := strings.Index(string(template), "\n")
		if newLineIdex == -1 {
			return nil, errors.New("unable to find new line after comment line")
		}

		newTemplate := []byte{}
		newTemplate = append(newTemplate, template[:commentIdx]...)
		newTemplate = append(newTemplate, template[newLineIdex+1:]...)
		template = newTemplate
	}

	return template, nil
}

// readTypes read all types in the proto file to be able to list them and identify those who are
// not part of another type (as a field).
func getTypes(pf pbparser.ProtoFile) (
	map[string]pbparser.MessageElement,
	map[string]pbparser.MessageElement,
	map[string]pbparser.EnumElement,
) {
	allTypes := map[string]pbparser.MessageElement{}
	rootTypes := map[string]pbparser.MessageElement{}
	enumTypes := map[string]pbparser.EnumElement{}

	for _, m := range pf.Messages {
		allTypes[m.Name] = m
		rootTypes[m.Name] = m
	}

	for _, e := range pf.Enums {
		enumTypes[e.Name] = e
	}

	for _, m := range pf.Messages {
		for _, mf := range m.Fields {
			if namedType, ok := mf.Type.(pbparser.NamedDataType); ok {
				// if a type is used as a field type in another message then it is not a root type
				delete(rootTypes, namedType.Name())
			}
		}
	}

	return allTypes, rootTypes, enumTypes
}

func writeHeaders(w io.StringWriter) error {
	_, err := w.WriteString(`
| Name | Description |
|------|-------------|
`)
	return err
}

// writeRows write the env var rows in the table
func writeRows(
	w io.StringWriter,
	m pbparser.MessageElement,
	types map[string]pbparser.MessageElement,
	enumTypes map[string]pbparser.EnumElement,
	prefix string,
) error {
	for _, mf := range m.Fields {
		if mf.Label == "repeated" {
			documentation := strings.Trim(toFieldDescription(mf.Name, mf.Documentation), ".")

			if len(documentation) > 0 {
				if e, ok := enumTypes[mf.Type.Name()]; ok { // isEnum
					examples := []string{}
					for _, ev := range e.EnumConstants[0:2] { // limit example to the first two, excluding unspecified
						if !strings.Contains(ev.Name, "UNSPECIFIED") {
							examples = append(examples, ev.Name)
						}
					}
					documentation += fmt.Sprintf(
						" e.g. `%s=\"%s\"`",
						prefix+toEnvFormat(mf.Name),
						strings.Join(examples, ","),
					)
				} else if strings.HasPrefix(mf.Type.Name(), "google.protobuf.") {
					documentation += ". The values should be separated by `,`"
				}

				_, err := w.WriteString(fmt.Sprintf(
					"| %s | %s. |\n",
					prefix+toEnvFormat(mf.Name),
					documentation,
				))
				if err != nil {
					return err
				}
			}
		} else if strings.HasPrefix(mf.Type.Name(), "google.protobuf.") { // i.e. it is scalar
			_, err := w.WriteString(fmt.Sprintf(
				"| %s | %s |\n",
				prefix+toEnvFormat(mf.Name),
				toFieldDescription(mf.Name, mf.Documentation),
			))
			if err != nil {
				return err
			}
		} else if _, ok := mf.Type.(pbparser.NamedDataType); ok {
			err := writeRows(w, types[mf.Type.Name()], types, enumTypes, prefix+toEnvFormat(mf.Name)+"_")
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func toEnvFormat(name string) string {
	return strings.ToUpper(strcase.ToSnake(name))
}

func toFieldDescription(name, description string) string {
	if len(description) == 0 {
		return ""
	}

	if strings.HasPrefix(description, name) {
		description = strings.Trim(description[len(name)+1:], " ")
	}

	return strings.ToUpper(string(description[0])) + description[1:]
}
