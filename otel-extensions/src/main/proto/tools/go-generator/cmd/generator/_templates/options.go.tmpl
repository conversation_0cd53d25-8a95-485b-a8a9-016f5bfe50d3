{{ .Header}}

package v1

import (
	"log"

	"google.golang.org/protobuf/proto"
)

type opts struct {
	prefix        string
	defaultConfig *{{ .MainType}}
}

var defaultOptions = opts {
	prefix:        "{{ .EnvPrefix}}",
	defaultConfig: &{{ .MainType}}{},
}

type LoadOption func(o *opts)

func WithEnvPrefix(prefix string) LoadOption {
	return func(o *opts) {
		o.prefix = prefix
	}
}

func WithDefaults(defaults *{{ .MainType}}) LoadOption {
	return func(o *opts) {
		// The reason why we clone the message instead of reusing the one passed by the user
		// is because user might decide to change values in runtime and that is undesirable
		// without a proper API.
		var ok bool
		o.defaultConfig, ok = proto.Clone(defaults).(*{{ .MainType}})
		if !ok {
			log.Fatal("failed to initialize config.")
		}
	}
}
