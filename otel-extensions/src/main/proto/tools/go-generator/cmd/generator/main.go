package main

import (
	"flag"
	"fmt"
	"go/format"
	"log"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/iancoleman/strcase"
	"github.com/tallstoat/pbparser"
)

var typeMapForRepeatedFields = map[string]string{
	"google.protobuf.StringValue": "wrappers.String",
}

func getGoPackageName(pf pbparser.ProtoFile) string {
	for _, opt := range pf.Options {
		if opt.Name == "go_package" {
			return opt.Value
		}
	}
	return pf.PackageName
}

var zeroValues = map[string]string{
	"string": `""`,
	"bool":   `false`,
}

func toPublicFieldName(name string) string {
	return strings.Title(name)
}

func isEnum(pf pbparser.ProtoFile, name string) bool {
	for _, e := range pf.Enums {
		if e.Name == name {
			return true
		}
	}

	return false
}

func shouldSkipOtherLanguageAgent(typeName string) bool {
	return strings.HasSuffix(typeName, "Agent") && !strings.HasPrefix(typeName, "Go")
}

func main() {
	var outDir = flag.String("out", ".", "Out directory for the generated code.")
	var optModule = flag.String("opt-module", ".", "Module for the generated code.")
	var envPrefix = flag.String("env-prefix", "HT_", "Prefix for env var loading")
	flag.Parse()

	if len(flag.Args()) == 0 {
		fmt.Println(`Usage: generator PROTO_FILE
Parse PROTO_FILE and generate output value objects`)
		return
	}

	rootDir := flag.Arg(0)

	cmdDir, err := os.Getwd()
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	err = filepath.Walk(
		rootDir,
		func(fpath string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if info.IsDir() || !strings.HasSuffix(fpath, ".proto") {
				return nil
			}

			if err = writeLoadersForProto(cmdDir, fpath, *outDir, *optModule, *envPrefix); err != nil {
				return err
			}

			return nil
		},
	)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

const generatedHeader = "// Code generated by github.com/hypertrace/agent-config/tools/go-generator. DO NOT EDIT."

func generateLoaderForProtoFile(pkgFqpn string, pf pbparser.ProtoFile) ([]byte, error) {
	c := fmt.Sprintf("%s\n\n", generatedHeader)
	c += fmt.Sprintf("package %s\n\n", pkgFqpn[strings.LastIndex(pkgFqpn, "/")+1:])
	// even if we don't need this import, worth to add it as gofmt is going to remove it.
	c += "import wrappers \"google.golang.org/protobuf/types/known/wrapperspb\"\n\n"

	for _, m := range pf.Messages {
		if shouldSkipOtherLanguageAgent(m.Name) {
			continue
		}

		mapFields := []pbparser.FieldElement{}

		c += "// loadFromEnv loads the data from env vars, defaults and makes sure all values are initialized.\n"
		c += fmt.Sprintf("func (x *%s) loadFromEnv(prefix string, defaultValues *%s) {\n", m.Name, m.Name)
		for _, mf := range m.Fields {
			if shouldSkipOtherLanguageAgent(mf.Type.Name()) {
				continue
			}

			if mf.Label == "oneof" {
				// currently we don't have a way to handle oneof labels
				// in env vars.
				continue
			}

			fieldName := toPublicFieldName(strcase.ToCamel(mf.Name))
			envPrefix := strings.ToUpper(strcase.ToSnake(mf.Name))
			fieldType := mf.Type.Name()
			if mf.Label == "repeated" {
				c += fmt.Sprintf(
					"    if rawVals, ok := getArrayStringEnv(prefix + \"%s\"); ok {\n",
					strings.ToUpper(mf.Name),
				)

				if namedType, ok := mf.Type.(pbparser.NamedDataType); ok {
					if isEnum(pf, namedType.Name()) {
						c += fmt.Sprintf(`		vals := []%s{}
						for _, rawVal := range rawVals {
							vals = append(vals, %s(%s_value[rawVal]))
						}
					`, namedType.Name(), namedType.Name(), namedType.Name())
						c += fmt.Sprintf(" x.%s = vals\n", fieldName)
					} else {
						// If this errors need to add additional type & target type cast to typeMapForRepeatedFields
						c += fmt.Sprintf(`for _, val := range rawVals {
							x.%s = append(x.%s, %s(val))
						}`, fieldName, fieldName, typeMapForRepeatedFields[namedType.Name()])
					}
				}
				c += fmt.Sprintf("    } else if len(x.%s) == 0 && defaultValues != nil && len(defaultValues.%s) > 0 {\n", fieldName, fieldName)
				c += fmt.Sprintf("        x.%s = defaultValues.%s\n", fieldName, fieldName)
				c += fmt.Sprintf("    }\n\n")
			} else if strings.HasPrefix(fieldType, "google.protobuf.") {
				_type := mf.Type.Name()[16 : len(mf.Type.Name())-5] // 16 = len("google.protobuf.")
				c += fmt.Sprintf(
					"    if val, ok := get%sEnv(prefix + \"%s\"); ok {\n",
					strings.Title(_type),
					envPrefix,
				)
				c += fmt.Sprintf("        x.%s = &wrappers.%sValue{Value: val}\n", fieldName, _type)
				c += fmt.Sprintf("    } else if x.%s == nil {\n", fieldName)
				c += "        // when there is no value to set we still prefer to initialize the variable to avoid\n"
				c += "        // `nil` checks in the consumers.\n"
				c += fmt.Sprintf("        x.%s = new(wrappers.%sValue)\n", fieldName, _type)
				c += fmt.Sprintf("        if defaultValues != nil && defaultValues.%s != nil {\n", fieldName)
				c += fmt.Sprintf("            x.%s = &wrappers.%sValue{Value: defaultValues.%s.Value}\n", fieldName, _type, fieldName)
				c += "        }\n"
				c += "    }\n"
			} else if namedType, ok := mf.Type.(pbparser.NamedDataType); ok {
				if isEnum(pf, namedType.Name()) {
					c += fmt.Sprintf(
						"    if rawVal, ok := getStringEnv(prefix + \"%s\"); ok {\n",
						envPrefix,
					)
					c += fmt.Sprintf("        x.%s = %s(%s_value[rawVal])\n", fieldName, namedType.Name(), namedType.Name())
					c += fmt.Sprintf("    } else if x.%s == %s(0) && defaultValues != nil && defaultValues.%s != %s(0) {\n", fieldName, fieldName, fieldName, fieldName)
					c += fmt.Sprintf("        x.%s = defaultValues.%s\n", fieldName, fieldName)
					c += fmt.Sprintf("    }\n\n")
				} else {
					c += fmt.Sprintf("    if x.%s == nil { x.%s = new(%s) }\n", fieldName, fieldName, namedType.Name())
					c += fmt.Sprintf("    if defaultValues == nil {\n")
					c += fmt.Sprintf("        x.%s.loadFromEnv(prefix + \"%s_\", nil)\n", fieldName, envPrefix)
					c += fmt.Sprintf("    } else {\n")
					c += fmt.Sprintf("        x.%s.loadFromEnv(prefix + \"%s_\", defaultValues.%s)\n", fieldName, envPrefix, fieldName)
					c += fmt.Sprintf("    }\n\n")
				}
			} else if strings.HasPrefix(fieldType, "map") {
				mapFields = append(mapFields, mf)
				c += fmt.Sprintf("    if defaultValues != nil && len(defaultValues.%s) > 0 {\n", fieldName)

				kType, vType := getSubtypesFromMap(mf.Type.Name())
				c += fmt.Sprintf("        if x.%s == nil { x.%s = make(map[%s]%s) } \n", fieldName, fieldName, kType, vType)
				c += fmt.Sprintf("        for k, v := range defaultValues.%s {\n", fieldName)
				c += "            // defaults should not override existing resource attributes unless empty\n"
				c += fmt.Sprintf("            if _, ok := x.%s[k]; !ok {", fieldName)
				c += fmt.Sprintf("                x.%s[k] = v\n", fieldName)
				c += "            }\n"
				c += "        }\n"
				c += "    }\n\n"
			} else {
				c += fmt.Sprintf(
					"    if val, ok := get%sEnv(prefix + \"%s\"); ok {\n",
					strings.Title(fieldType),
					envPrefix,
				)
				c += fmt.Sprintf("        x.%s = val\n", fieldName)
				c += fmt.Sprintf("    } else if x.%s == %s && defaultValues != nil && defaultValues.%s != %s {\n", fieldName, zeroValues[fieldType], fieldName, zeroValues[fieldType])
				c += fmt.Sprintf("        x.%s = defaultValues.%s\n", fieldName, fieldName)
				c += fmt.Sprintf("    }\n\n")
			}
		}
		c += "}\n\n"

		if len(mapFields) > 0 {
			for _, mf := range mapFields {
				addMapFieldSetter(&c, m, mf)
				c += "\n"
			}
		}
	}

	bc := []byte(c)
	fbc, err := format.Source(bc)
	if err != nil {
		fmt.Printf("failed to format the content, writing unformatted: %v\n", err)
		return bc, nil
	}

	return fbc, nil
}

// writeLoadersForProto generates all the loader config for the proto object
func writeLoadersForProto(cmdDir, protoFilepath, outDir, optModule, envPrefix string) error {
	f, err := os.Open(protoFilepath)
	if err != nil {
		return fmt.Errorf("Unable to open the proto file %q: %v", protoFilepath, err)
	}
	defer f.Close()

	pf, err := pbparser.Parse(f, &protobufImportModuleProvider{cmdDir})
	if err != nil {
		return fmt.Errorf("Unable to parse proto file %q: %v \n", protoFilepath, err)
	}

	pkgFqpn := getGoPackageName(pf)
	genDstDir := generatedOutputDir(outDir, pkgFqpn, optModule)

	protoLoaderContent, err := generateLoaderForProtoFile(pkgFqpn, pf)
	if err != nil {
		return fmt.Errorf("failed to generate loaders: %v", err)
	}

	err = writeToFile(path.Join(genDstDir, getPBLoaderFilename(protoFilepath)), protoLoaderContent)
	if err != nil {
		return fmt.Errorf("failed to write loaders file: %v", err)
	}

	templateVars := Loaders{
		MainType:  "AgentConfig",
		Header:    generatedHeader,
		EnvPrefix: envPrefix,
	}

	err = copyTemplateFiles(path.Join(cmdDir, "_templates"), genDstDir, templateVars)
	if err != nil {
		return fmt.Errorf("failed to copy template files: %v", err)
	}

	goModContent, err := generateGoModFile(genDstDir, pkgFqpn)
	if err != nil {
		return fmt.Errorf("failed to create go.mod file: %v", err)
	}

	err = writeToFile(path.Join(path.Dir(genDstDir), "go.mod"), goModContent)
	if err != nil {
		return fmt.Errorf("failed to write loaders file: %v", err)
	}

	return nil
}

func generatedOutputDir(outDir, pkgFqpn, optModule string) string {
	return path.Join(outDir, pkgFqpn[len(optModule):])
}

func getPBLoaderFilename(protoFilepath string) string {
	protoBaseFilename := filepath.Base(protoFilepath)
	return protoBaseFilename[0:len(protoBaseFilename)-6] + ".pbloader.go" // 6 = len(".proto")
}

func generateGoModFile(outDir, pkgname string) ([]byte, error) {
	idx := strings.LastIndex(pkgname, "/")
	return []byte(fmt.Sprintf("module %s\n", pkgname[:idx])), nil
}

func getSubtypesFromMap(_type string) (string, string) {
	_type = strings.Replace(_type, " ", "", -1)
	t := strings.Split(_type[4:len(_type)-1], ",")
	if t[0] != "string" || t[1] != "string" {
		// While it is possible things work smooth, we better file and refine this rule
		// for field map setters.
		log.Fatalf("unsupported map subtypes: key %q value %q", t[0], t[1])
	}

	return t[0], t[1]
}

func addMapFieldSetter(c *string, m pbparser.MessageElement, mf pbparser.FieldElement) {
	fieldName := toPublicFieldName(strcase.ToCamel(mf.Name))
	*c += fmt.Sprintf("// Put%s sets values in the %s map.\n", fieldName, fieldName)

	kType, vType := getSubtypesFromMap(mf.Type.Name())

	*c += fmt.Sprintf("func (x *%s) Put%s(m map[%s]%s) {\n", m.Name, fieldName, kType, vType)
	*c += fmt.Sprintf("    if len(m) == 0 { return }\n")
	*c += fmt.Sprintf("    if x.%s == nil { x.%s = make(map[%s]%s) } \n", fieldName, fieldName, kType, vType)
	*c += "    for k, v := range m {\n"
	*c += fmt.Sprintf("            x.%s[k] = v\n", fieldName)
	*c += "    }\n"
	*c += "}\n"
}

func writeToFile(filename string, content []byte) error {
	f, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file %q: %v", filename, err)
	}
	defer f.Close()
	_, err = f.Write(content)
	if err != nil {
		return fmt.Errorf("failed to write into file %q: %v", filename, err)
	}

	return nil
}
