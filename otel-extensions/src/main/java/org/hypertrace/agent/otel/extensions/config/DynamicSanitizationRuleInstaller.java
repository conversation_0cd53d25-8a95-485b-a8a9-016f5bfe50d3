/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.otel.extensions.config;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.tooling.BeforeAgentListener;
import io.opentelemetry.sdk.autoconfigure.AutoConfiguredOpenTelemetrySdk;
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * An AgentListener implementation that initializes and starts the DynamicSanitizationRuleManager
 * during agent startup. This ensures that sanitization rules are loaded and kept up-to-date
 * throughout the application lifecycle.
 */
@AutoService(BeforeAgentListener.class)
public class DynamicSanitizationRuleInstaller implements BeforeAgentListener {

  private static final Logger logger =
      LoggerFactory.getLogger(DynamicSanitizationRuleInstaller.class);

  private static volatile DynamicSanitizationRuleManager ruleManager;

  @Override
  public void beforeAgent(AutoConfiguredOpenTelemetrySdk autoConfiguredOpenTelemetrySdk) {
    try {
      System.out.println("[HT-Agent] DynamicSanitizationRuleInstaller.beforeAgent() called");

      // Initialize the dynamic sanitization rule manager
      ruleManager = new DynamicSanitizationRuleManager();
      System.out.println("[HT-Agent] DynamicSanitizationRuleManager created");

      // Set as singleton instance for global access
      DynamicSanitizationRuleManager.setInstance(ruleManager);
      System.out.println("[HT-Agent] Singleton instance set");

      // Start the periodic rule fetching
      ruleManager.start();
      System.out.println("[HT-Agent] Rule manager started");

      logger.info("Dynamic sanitization rule manager initialized and started");
      System.out.println("[HT-Agent] Dynamic sanitization rule manager initialized and started");

      // Register shutdown hook to properly stop the rule manager
      Runtime.getRuntime()
          .addShutdownHook(
              new Thread(
                  () -> {
                    if (ruleManager != null) {
                      logger.info("Shutting down dynamic sanitization rule manager");
                      ruleManager.stop();
                      // Clear singleton instance
                      DynamicSanitizationRuleManager.setInstance(null);
                    }
                  },
                  "sanitization-rule-manager-shutdown"));

    } catch (Exception e) {
      logger.error("Failed to initialize dynamic sanitization rule manager", e);
      // Don't fail the entire agent startup if rule manager fails to initialize
      // The agent should still work with static configuration
    }
  }

  @Override
  public int order() {
    // Start after basic configs are loaded but before filters are initialized
    // This ensures the rule manager is ready when filters need it
    return 5;
  }

  /**
   * Get the current rule manager instance (for testing or manual access)
   *
   * @return the current DynamicSanitizationRuleManager instance, or null if not initialized
   */
  public static DynamicSanitizationRuleManager getRuleManager() {
    return DynamicSanitizationRuleManager.getInstance();
  }
}
