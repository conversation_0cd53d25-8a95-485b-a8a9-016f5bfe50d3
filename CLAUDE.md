# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Build Commands
- `make build` - Build the project without running smoke tests
- `make assemble` - Assemble all modules
- `./gradlew build -x :smoke-tests:test` - Gradle build excluding smoke tests
- `./gradlew assemble` - Gradle assemble

### Testing Commands  
- `make test` - Run all tests including smoke tests
- `./gradlew check` - Run all checks (tests, spotless, etc.)
- `make smoke-test` - Run smoke tests only (requires Docker via Testcontainers)
- `./gradlew :smoke-tests:test -PsmokeTestSuite=other` - Run specific smoke test suite
- `./gradlew test --tests "ClassName.methodName"` - Run single test

### Code Quality Commands
- `make format` - Format code using Spotless
- `./gradlew spotlessApply` - Apply code formatting
- `./gradlew spotlessCheck` - Check code formatting
- `make dependency-check` - Run OWASP dependency check

### Other Commands
- `make init-submodules` - Initialize git submodules
- `make muzzle` - Run muzzle checks for instrumentation compatibility

## Project Architecture

This is a Hypertrace distribution of OpenTelemetry Java agent that adds advanced capabilities for request/response capture and data sanitization.

### Core Components

#### Main Agent Entry Point
- `javaagent/` - Contains `HypertraceAgent.java`, the main entry point that wraps OpenTelemetry agent
- Final artifact: `javaagent/build/libs/hypertrace-agent-<version>-all.jar`

#### Core Libraries
- `javaagent-core/` - Core instrumentation utilities, semantic attributes, config interfaces
- `javaagent-tooling/` - Tooling support for instrumentation
- `javaagent-bootstrap/` - Bootstrap classes loaded early in JVM
- `otel-extensions/` - OpenTelemetry SDK extensions and configuration

#### Filter System
- `filter-api/` - Pluggable filter API for request evaluation and data sanitization
- Filters can block requests or sanitize sensitive data (passwords, API keys, etc.)
- Implements SPI pattern via `FilterProvider` service loading

#### Instrumentation Modules
Located in `instrumentation/` directory, organized by framework:
- `grpc-1.6/`, `grpc-common/`, `grpc-shaded-netty-1.9/` - gRPC instrumentation  
- `servlet/servlet-3.0/`, `servlet/servlet-5.0/`, `servlet/servlet-rw/` - Servlet instrumentation
- `netty/netty-4.0/`, `netty/netty-4.1/` - Netty instrumentation
- `okhttp/okhttp-3.0/` - OkHttp client instrumentation
- `apache-httpclient-4.0/`, `apache-httpasyncclient-4.1/` - Apache HTTP client instrumentation
- `vertx/vertx-web-3.0/` - Vert.x instrumentation
- `spring/spring-webflux-5.0/` - Spring WebFlux instrumentation
- `undertow/` - Undertow instrumentation
- `kafka-clients-2.6/` - Kafka instrumentation
- `micronaut-1.0/`, `micronaut-3.0/` - Micronaut instrumentation
- `struts-2.3/`, `spark-2.3/`, `jaxrs-client-2.0/` - Other framework instrumentation

#### Testing Infrastructure
- `testing-common/` - Shared test utilities and OTLP receiver
- `smoke-tests/` - Integration tests using Testcontainers
- `tests-extension/` - Test extensions and mock filters

### Key Features
- **Request/Response Capture**: Headers and bodies for HTTP, gRPC, Kafka
- **Automatic Data Sanitization**: Masks sensitive data like passwords, API keys, credit cards
- **Pluggable Filters**: SPI-based filter system for custom evaluation logic
- **Multi-framework Support**: Covers major Java frameworks and libraries

### Development Notes
- Java 8 compatibility required
- Uses Google Java Format via Spotless
- OpenTelemetry agent version 1.33.0
- Gradle multi-module project with 74 modules
- When running tests from IDE, set `SMOKETEST_JAVAAGENT_PATH` env variable
- Tests require Docker for Testcontainers

### Instrumentation Naming
Hypertrace-specific instrumentations can be disabled with:
- `ht` - all Hypertrace instrumentations  
- `servlet-ht` - Servlet, Spark Web
- `okhttp-ht` - OkHttp
- `grpc-ht` - gRPC

Standard OpenTelemetry instrumentation names also work and disable both core and Hypertrace instrumentations.