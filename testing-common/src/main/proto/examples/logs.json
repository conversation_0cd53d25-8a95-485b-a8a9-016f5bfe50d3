{"resourceLogs": [{"resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "my.service"}}]}, "scopeLogs": [{"scope": {"name": "my.library", "version": "1.0.0", "attributes": [{"key": "my.scope.attribute", "value": {"stringValue": "some scope attribute"}}]}, "logRecords": [{"timeUnixNano": "1544712660300000000", "observedTimeUnixNano": "1544712660300000000", "severityNumber": 10, "severityText": "Information", "traceId": "5B8EFFF798038103D269B633813FC60C", "spanId": "EEE19B7EC3C1B174", "body": {"stringValue": "Example log record"}, "attributes": [{"key": "string.attribute", "value": {"stringValue": "some string"}}, {"key": "boolean.attribute", "value": {"boolValue": true}}, {"key": "int.attribute", "value": {"intValue": "10"}}, {"key": "double.attribute", "value": {"doubleValue": 637.704}}, {"key": "array.attribute", "value": {"arrayValue": {"values": [{"stringValue": "many"}, {"stringValue": "values"}]}}}, {"key": "map.attribute", "value": {"kvlistValue": {"values": [{"key": "some.map.key", "value": {"stringValue": "some value"}}]}}}]}]}]}]}