{"ignorePatterns": [{"pattern": "^https://github\\.com/open-telemetry/opentelemetry-specification/(issues|pull)"}], "replacementPatterns": [{"pattern": "^/", "replacement": "{{BASEURL}}/"}, {"pattern": "^https://github.com/open-telemetry/opentelemetry-proto/(blob|tree)/[^/]+/docs/", "replacement": "LINK-CHECK-ERROR-USE-LOCAL-PATH-TO-DOC-PAGE-NOT-EXTERNAL-URL/"}], "retryOn429": true, "aliveStatusCodes": [200, 403]}