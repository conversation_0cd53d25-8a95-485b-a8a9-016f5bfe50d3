// Copyright 2020, OpenTelemetry Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package opentelemetry.proto.logs.v1;

import "opentelemetry/proto/common/v1/common.proto";
import "opentelemetry/proto/resource/v1/resource.proto";

option csharp_namespace = "OpenTelemetry.Proto.Logs.V1";
option java_multiple_files = true;
option java_package = "io.opentelemetry.proto.logs.v1";
option java_outer_classname = "LogsProto";
option go_package = "go.opentelemetry.io/proto/otlp/logs/v1";

// LogsData represents the logs data that can be stored in a persistent storage,
// OR can be embedded by other protocols that transfer OTLP logs data but do not
// implement the OTLP protocol.
//
// The main difference between this message and collector protocol is that
// in this message there will not be any "control" or "metadata" specific to
// OTLP protocol.
//
// When new fields are added into this message, the OTLP request MUST be updated
// as well.
message LogsData {
  // An array of ResourceLogs.
  // For data coming from a single resource this array will typically contain
  // one element. Intermediary nodes that receive data from multiple origins
  // typically batch the data before forwarding further and in that case this
  // array will contain multiple elements.
  repeated ResourceLogs resource_logs = 1;
}

// A collection of ScopeLogs from a Resource.
message ResourceLogs {
  reserved 1000;

  // The resource for the logs in this message.
  // If this field is not set then resource info is unknown.
  opentelemetry.proto.resource.v1.Resource resource = 1;

  // A list of ScopeLogs that originate from a resource.
  repeated ScopeLogs scope_logs = 2;

  // The Schema URL, if known. This is the identifier of the Schema that the resource data
  // is recorded in. To learn more about Schema URL see
  // https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
  // This schema_url applies to the data in the "resource" field. It does not apply
  // to the data in the "scope_logs" field which have their own schema_url field.
  string schema_url = 3;
}

// A collection of Logs produced by a Scope.
message ScopeLogs {
  // The instrumentation scope information for the logs in this message.
  // Semantically when InstrumentationScope isn't set, it is equivalent with
  // an empty instrumentation scope name (unknown).
  opentelemetry.proto.common.v1.InstrumentationScope scope = 1;

  // A list of log records.
  repeated LogRecord log_records = 2;

  // The Schema URL, if known. This is the identifier of the Schema that the log data
  // is recorded in. To learn more about Schema URL see
  // https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
  // This schema_url applies to all logs in the "logs" field.
  string schema_url = 3;
}

// Possible values for LogRecord.SeverityNumber.
enum SeverityNumber {
  // UNSPECIFIED is the default SeverityNumber, it MUST NOT be used.
  SEVERITY_NUMBER_UNSPECIFIED = 0;
  SEVERITY_NUMBER_TRACE  = 1;
  SEVERITY_NUMBER_TRACE2 = 2;
  SEVERITY_NUMBER_TRACE3 = 3;
  SEVERITY_NUMBER_TRACE4 = 4;
  SEVERITY_NUMBER_DEBUG  = 5;
  SEVERITY_NUMBER_DEBUG2 = 6;
  SEVERITY_NUMBER_DEBUG3 = 7;
  SEVERITY_NUMBER_DEBUG4 = 8;
  SEVERITY_NUMBER_INFO   = 9;
  SEVERITY_NUMBER_INFO2  = 10;
  SEVERITY_NUMBER_INFO3  = 11;
  SEVERITY_NUMBER_INFO4  = 12;
  SEVERITY_NUMBER_WARN   = 13;
  SEVERITY_NUMBER_WARN2  = 14;
  SEVERITY_NUMBER_WARN3  = 15;
  SEVERITY_NUMBER_WARN4  = 16;
  SEVERITY_NUMBER_ERROR  = 17;
  SEVERITY_NUMBER_ERROR2 = 18;
  SEVERITY_NUMBER_ERROR3 = 19;
  SEVERITY_NUMBER_ERROR4 = 20;
  SEVERITY_NUMBER_FATAL  = 21;
  SEVERITY_NUMBER_FATAL2 = 22;
  SEVERITY_NUMBER_FATAL3 = 23;
  SEVERITY_NUMBER_FATAL4 = 24;
}

// LogRecordFlags represents constants used to interpret the
// LogRecord.flags field, which is protobuf 'fixed32' type and is to
// be used as bit-fields. Each non-zero value defined in this enum is
// a bit-mask.  To extract the bit-field, for example, use an
// expression like:
//
//   (logRecord.flags & LOG_RECORD_FLAGS_TRACE_FLAGS_MASK)
//
enum LogRecordFlags {
  // The zero value for the enum. Should not be used for comparisons.
  // Instead use bitwise "and" with the appropriate mask as shown above.
  LOG_RECORD_FLAGS_DO_NOT_USE = 0;

  // Bits 0-7 are used for trace flags.
  LOG_RECORD_FLAGS_TRACE_FLAGS_MASK = 0x000000FF;

  // Bits 8-31 are reserved for future use.
}

// A log record according to OpenTelemetry Log Data Model:
// https://github.com/open-telemetry/oteps/blob/main/text/logs/0097-log-data-model.md
message LogRecord {
  reserved 4;

  // time_unix_nano is the time when the event occurred.
  // Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
  // Value of 0 indicates unknown or missing timestamp.
  fixed64 time_unix_nano = 1;

  // Time when the event was observed by the collection system.
  // For events that originate in OpenTelemetry (e.g. using OpenTelemetry Logging SDK)
  // this timestamp is typically set at the generation time and is equal to Timestamp.
  // For events originating externally and collected by OpenTelemetry (e.g. using
  // Collector) this is the time when OpenTelemetry's code observed the event measured
  // by the clock of the OpenTelemetry code. This field MUST be set once the event is
  // observed by OpenTelemetry.
  //
  // For converting OpenTelemetry log data to formats that support only one timestamp or
  // when receiving OpenTelemetry log data by recipients that support only one timestamp
  // internally the following logic is recommended:
  //   - Use time_unix_nano if it is present, otherwise use observed_time_unix_nano.
  //
  // Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
  // Value of 0 indicates unknown or missing timestamp.
  fixed64 observed_time_unix_nano = 11;

  // Numerical value of the severity, normalized to values described in Log Data Model.
  // [Optional].
  SeverityNumber severity_number = 2;

  // The severity text (also known as log level). The original string representation as
  // it is known at the source. [Optional].
  string severity_text = 3;

  // A value containing the body of the log record. Can be for example a human-readable
  // string message (including multi-line) describing the event in a free form or it can
  // be a structured data composed of arrays and maps of other values. [Optional].
  opentelemetry.proto.common.v1.AnyValue body = 5;

  // Additional attributes that describe the specific event occurrence. [Optional].
  // Attribute keys MUST be unique (it is not allowed to have more than one
  // attribute with the same key).
  repeated opentelemetry.proto.common.v1.KeyValue attributes = 6;
  uint32 dropped_attributes_count = 7;

  // Flags, a bit field. 8 least significant bits are the trace flags as
  // defined in W3C Trace Context specification. 24 most significant bits are reserved
  // and must be set to 0. Readers must not assume that 24 most significant bits
  // will be zero and must correctly mask the bits when reading 8-bit trace flag (use
  // flags & LOG_RECORD_FLAGS_TRACE_FLAGS_MASK). [Optional].
  fixed32 flags = 8;

  // A unique identifier for a trace. All logs from the same trace share
  // the same `trace_id`. The ID is a 16-byte array. An ID with all zeroes OR
  // of length other than 16 bytes is considered invalid (empty string in OTLP/JSON
  // is zero-length and thus is also invalid).
  //
  // This field is optional.
  //
  // The receivers SHOULD assume that the log record is not associated with a
  // trace if any of the following is true:
  //   - the field is not present,
  //   - the field contains an invalid value.
  bytes trace_id = 9;

  // A unique identifier for a span within a trace, assigned when the span
  // is created. The ID is an 8-byte array. An ID with all zeroes OR of length
  // other than 8 bytes is considered invalid (empty string in OTLP/JSON
  // is zero-length and thus is also invalid).
  //
  // This field is optional. If the sender specifies a valid span_id then it SHOULD also
  // specify a valid trace_id.
  //
  // The receivers SHOULD assume that the log record is not associated with a
  // span if any of the following is true:
  //   - the field is not present,
  //   - the field contains an invalid value.
  bytes span_id = 10;
}
