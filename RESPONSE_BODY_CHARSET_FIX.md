# Hypertrace Java Agent 响应体字符编码乱码修复

## 🔍 问题描述

在使用 Hypertrace Java Agent 监控 Spring Boot 应用时，<PERSON><PERSON><PERSON> 中存储的 `http.response.body` 数据出现中文乱码：

```json
// 乱码示例
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}

// 期望结果
{"id":5,"name":"测试用户","email":"****@example.com","phone":"****"}
```

## 🔍 根本原因分析

问题出现在**字符编码设置的时序**上：

1. **Buffer 创建时机**：当 `ServletResponse.getOutputStream()` 被调用时，Agent 创建 `BoundedByteArrayOutputStream` 并设置字符编码
2. **编码设置时序**：此时 `httpServletResponse.getCharacterEncoding()` 可能还没有被 Spring Boot 正确设置
3. **编码不匹配**：后续 Spring Boot 设置了正确的字符编码，但 buffer 已经用错误的字符编码创建了
4. **乱码产生**：在响应完成时，使用错误的字符编码解析响应体内容

### 时序图

```
1. ServletResponse.getOutputStream() 调用
   ↓
2. Agent 创建 BoundedByteArrayOutputStream
   ↓ (此时 getCharacterEncoding() 可能返回 null 或默认值)
3. 使用默认/错误的字符编码创建 buffer
   ↓
4. Spring Boot 设置正确的字符编码
   ↓
5. 响应体数据写入 buffer (使用正确编码)
   ↓
6. 响应完成时，使用错误的字符编码解析 buffer
   ↓
7. 产生乱码
```

## 🛠️ 解决方案

### 核心修复：延迟字符编码检查

在响应体捕获完成时，重新检查当前的字符编码，而不是使用创建 buffer 时的字符编码。

**修改前的问题代码**：
```java
// 在 buffer 创建时设置字符编码
String charsetStr = httpServletResponse.getCharacterEncoding();
Charset charset = ContentTypeCharsetUtils.toCharsetWithSpringBootSupport(charsetStr, contentType);
BoundedByteArrayOutputStream buffer = BoundedBuffersFactory.createStream(charset);

// 在响应完成时使用创建时的字符编码
span.setAttribute(
    HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
    DataSanitizationUtils.sanitize(buffer.toStringWithSuppliedCharset()));
```

**修复后的代码**：
```java
// 在响应完成时重新检查字符编码
try {
  // Re-check the character encoding at response completion time
  // as Spring Boot may have set it after the buffer was created
  String currentCharsetStr = httpServletResponse.getCharacterEncoding();
  String contentType = httpServletResponse.getContentType();
  Charset currentCharset = ContentTypeCharsetUtils.toCharsetWithSpringBootSupport(currentCharsetStr, contentType);
  
  // Convert the buffer content using the current (correct) charset
  String responseBody = new String(buffer.toByteArray(), currentCharset);
  
  span.setAttribute(
      HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
      DataSanitizationUtils.sanitize(responseBody));
} catch (Exception e) {
  // Fallback to the original method if there's any issue
  span.setAttribute(
      HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
      DataSanitizationUtils.sanitize(buffer.toStringWithSuppliedCharset()));
}
```

## 📁 修改的文件

1. **Servlet 5.0**：
   - `instrumentation/servlet/servlet-5.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v5_0/Utils.java`

2. **Servlet 3.0**：
   - `instrumentation/servlet/servlet-3.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v3_0/nowrapping/Utils.java`

## ✅ 修复效果

### 修复前
```json
// Jaeger 中的 http.response.body
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}
```

### 修复后
```json
// Jaeger 中的 http.response.body
{"id":5,"name":"测试用户","email":"****@example.com","phone":"****"}
```

## 🔧 技术细节

### 关键改进点

1. **时序修复**：在响应完成时而不是 buffer 创建时检查字符编码
2. **动态检查**：使用 `httpServletResponse.getCharacterEncoding()` 获取当前字符编码
3. **正确转换**：使用 `new String(buffer.toByteArray(), currentCharset)` 进行正确的字符编码转换
4. **异常处理**：提供 fallback 机制确保稳定性

### 为什么这样修复有效

1. **Spring Boot 时序**：Spring Boot 通常在处理请求过程中设置字符编码，而不是在最开始
2. **编码一致性**：确保响应体解析使用的字符编码与实际写入时使用的字符编码一致
3. **向后兼容**：保留原有的 fallback 机制，确保不会破坏现有功能

## 🚀 使用方法

修复后的 Agent 会自动处理字符编码问题，无需额外配置：

```bash
java -javaagent:hypertrace-agent.jar -jar your-spring-boot-app.jar
```

## 🎯 适用场景

- Spring Boot 应用返回中文 JSON 数据
- 使用 UTF-8 编码的现代 Web 应用
- 需要在 Jaeger 中查看正确响应体内容的场景
- 任何涉及非 ASCII 字符的响应体

## 📝 注意事项

1. **兼容性**：修复保持向后兼容，不会影响现有应用
2. **性能**：增加了字符编码检查，但性能影响微乎其微
3. **稳定性**：提供了异常处理和 fallback 机制
4. **覆盖范围**：修复覆盖了 Servlet 3.0 和 5.0 两个版本

这个修复解决了 Spring Boot 应用中响应体中文乱码的根本问题，确保了 Jaeger 中存储的数据的正确性和可读性。
