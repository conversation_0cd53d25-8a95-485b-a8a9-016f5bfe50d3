# Dynamic Sanitization Rule Implementation

## Overview

This implementation adds dynamic sanitization rule management to the Hypertrace Java Agent, allowing rules to be loaded and updated from remote endpoints without requiring application restarts.

## Key Features

### 1. Dynamic Rule Loading
- **Remote Configuration**: Rules are fetched from configurable HTTP endpoints
- **Periodic Updates**: Rules are refreshed at configurable intervals (default: 5 minutes)
- **Authentication Support**: Bearer token authentication for secure endpoints
- **Service-Specific Rules**: Rules can be filtered by service name

### 2. Agent Integration
- **Startup Integration**: Rules are loaded during agent initialization
- **Lifecycle Management**: Proper startup and shutdown handling
- **Fallback Support**: Falls back to static configuration if dynamic loading fails
- **Error Resilience**: Network errors don't crash the application

### 3. Rule Types Supported
- **Field Name Rules**: Match based on JSON/XML/form field names
- **Pattern Rules**: Match using regular expressions
- **Content Type Rules**: Apply rules based on HTTP content types
- **Custom Rules**: Extensible for custom implementations

## Implementation Details

### Core Components

#### 1. DynamicSanitizationRuleManager
- **Location**: `filter-api/src/main/java/org/hypertrace/agent/filter/config/DynamicSanitizationRuleManager.java`
- **Purpose**: Manages the lifecycle of dynamic rule loading
- **Features**:
  - HTTP client for fetching rules from endpoints
  - Scheduled executor for periodic updates
  - Configuration validation
  - Singleton pattern for global access

#### 2. DynamicSanitizationRuleInstaller
- **Location**: `otel-extensions/src/main/java/org/hypertrace/agent/otel/extensions/config/DynamicSanitizationRuleInstaller.java`
- **Purpose**: Integrates rule manager with agent startup
- **Features**:
  - BeforeAgentListener implementation
  - Proper initialization order (after configs, before filters)
  - Shutdown hook registration

#### 3. DynamicSensitiveDataSanitizer
- **Location**: `filter-api/src/main/java/org/hypertrace/agent/filter/DynamicSensitiveDataSanitizer.java`
- **Purpose**: Applies dynamic rules to sanitize data
- **Features**:
  - Integration with rule manager singleton
  - Fallback to static sanitization
  - Support for multiple content types (JSON, XML, form data)

### Configuration

#### Environment Variables / System Properties

| Property | Environment Variable | Description | Default |
|----------|---------------------|-------------|---------|
| `ht.sanitization.config.endpoint` | `HT_SANITIZATION_CONFIG_ENDPOINT` | URL endpoint to fetch rules | None (disabled) |
| `ht.sanitization.config.auth.token` | `HT_SANITIZATION_CONFIG_AUTH_TOKEN` | Bearer token for authentication | None |
| `ht.sanitization.config.refresh.interval` | `HT_SANITIZATION_CONFIG_REFRESH_INTERVAL` | Refresh interval in seconds | 300 |
| `ht.service.name` | `HT_SERVICE_NAME` | Service name for rule filtering | "unknown-service" |

#### Rule Configuration Format

```json
{
  "version": "1.0.0",
  "timestamp": 1640995200000,
  "enabled": true,
  "markersEnabled": false,
  "markerFormat": "BRACKET",
  "rules": [
    {
      "id": "password-rule",
      "name": "Password Detection",
      "type": "FIELD_NAME",
      "severity": "HIGH",
      "enabled": true,
      "priority": 100,
      "fieldNames": ["password", "passwd", "pwd"],
      "maskValue": "****",
      "markerType": "PASSWORD"
    }
  ]
}
```

### Integration Points

#### 1. Agent Startup Sequence
1. **Configuration Loading**: Basic agent configs are loaded (order 0)
2. **Rule Manager Initialization**: Dynamic rule manager starts (order 5)
3. **Filter Initialization**: Filters are created and can access rules (order 10+)

#### 2. Filter System Integration
- Rules are accessed via singleton pattern
- Fallback to static configuration when dynamic rules unavailable
- Seamless integration with existing filter providers

#### 3. Service Loading
- Uses Java ServiceLoader pattern for filter discovery
- Automatic registration via `@AutoService` annotations
- No manual configuration required

## Testing

### Unit Tests
- **DynamicSanitizationRuleInstallerTest**: Tests agent integration
- **Location**: `otel-extensions/src/test/java/org/hypertrace/agent/otel/extensions/config/`
- **Coverage**: Initialization, lifecycle, singleton management

### Integration Testing
- Rules can be tested with mock HTTP servers
- Fallback behavior verification
- Performance impact assessment

## Documentation

### User Documentation
- **Configuration Guide**: `docs/dynamic-sanitization-configuration.md`
- **Usage Examples**: `examples/dynamic-sanitization-example.md`
- **API Reference**: Inline Javadoc comments

### Developer Documentation
- **Architecture Overview**: This document
- **Code Comments**: Comprehensive inline documentation
- **Test Examples**: Unit and integration test examples

## Benefits

### 1. Operational Flexibility
- **No Restarts Required**: Rules can be updated without application downtime
- **Centralized Management**: Rules managed from a single configuration service
- **Service-Specific Rules**: Different rules for different services
- **Gradual Rollout**: Rules can be tested and rolled out incrementally

### 2. Security Improvements
- **Real-time Updates**: New sensitive data patterns can be addressed immediately
- **Compliance Support**: Rules can be updated to meet changing compliance requirements
- **Audit Trail**: Configuration changes can be tracked and audited

### 3. Performance Considerations
- **Efficient Caching**: Rules are cached and patterns are pre-compiled
- **Minimal Overhead**: Network calls only during scheduled updates
- **Graceful Degradation**: Falls back to static rules if dynamic loading fails

## Future Enhancements

### Potential Improvements
1. **Rule Validation**: Enhanced validation for rule syntax and conflicts
2. **Metrics Integration**: Expose metrics about rule application and performance
3. **Hot Reloading**: Support for immediate rule updates via webhooks
4. **Rule Testing**: Built-in testing framework for rule validation
5. **Configuration UI**: Web interface for rule management

### Extensibility Points
1. **Custom Rule Types**: Framework for implementing custom rule types
2. **Multiple Endpoints**: Support for multiple configuration sources
3. **Rule Inheritance**: Hierarchical rule inheritance and overrides
4. **Conditional Rules**: Rules that apply based on runtime conditions

## Conclusion

This implementation provides a robust, production-ready solution for dynamic sanitization rule management in the Hypertrace Java Agent. It maintains backward compatibility while adding powerful new capabilities for managing sensitive data detection and sanitization in distributed systems.
