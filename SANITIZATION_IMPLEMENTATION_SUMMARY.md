# Data Sanitization Implementation Summary

## Overview
This document summarizes the implementation of DataSanitizationUtils wrapping for request and response body extraction across the javaagent instrumentation modules.

## Changes Made

### 1. Enhanced DataSanitizationUtils
- Added import for `java.nio.charset.Charset` and `java.nio.charset.StandardCharsets`
- Added overloaded methods:
  - `sanitize(String body)` - Simplified version without headers
  - `sanitize(byte[] body, Charset charset)` - For byte array body content
  - `sanitize(byte[] body, Charset charset, Map<String, String> headers)` - For byte array with headers

### 2. Updated Core Buffer Classes
All buffer pair classes now use DataSanitizationUtils.sanitize:

- **ByteBufferSpanPair**: Modified `captureBody` to call `DataSanitizationUtils.sanitize(requestBody, headers)`
- **CharBufferSpanPair**: Modified `captureBody` to call `DataSanitizationUtils.sanitize(requestBody, headers)`
- **StringMapSpanPair**: Modified `captureBody` to call `DataSanitizationUtils.sanitize(json, headers)`

### 3. Updated Instrumentation Modules

#### Netty (4.0 and 4.1)
- **DataCaptureUtils**: Replaced `span.setAttribute(attributeKey.name(), body)` with `span.setAttribute(attributeKey.name(), DataSanitizationUtils.sanitize(body))`

#### Apache HttpClient 4.0
- **ApacheHttpClientUtils**: Replaced `span.setAttribute(bodyAttributeKey, body)` with `span.setAttribute(bodyAttributeKey, DataSanitizationUtils.sanitize(body))`

#### OkHttp 3.0
- **OkHttpTracingInterceptor**: 
  - Request body: `span.setAttribute(HypertraceSemanticAttributes.HTTP_REQUEST_BODY, DataSanitizationUtils.sanitize(buffer.readUtf8()))`
  - Response body: `span.setAttribute(HypertraceSemanticAttributes.HTTP_RESPONSE_BODY, DataSanitizationUtils.sanitize(body))`

#### Undertow 1.4
- **BodyCapturingExchangeCompletionListener**: Replaced `span.setAttribute(HypertraceSemanticAttributes.HTTP_REQUEST_BODY, body)` with `span.setAttribute(HypertraceSemanticAttributes.HTTP_REQUEST_BODY, DataSanitizationUtils.sanitize(body))`

#### Servlet (3.0 and 5.0)
- **Utils**: Updated `captureResponseBody` method to use DataSanitizationUtils.sanitize for both ServletOutputStream and PrintWriter cases

#### Vertx Web 3.0
- **ResponseBodyWrappingHandler**: Updated both recording and non-recording span cases to use `DataSanitizationUtils.sanitize(responseBody)`

## Impact
All request and response bodies captured by the instrumentation will now be processed through DataSanitizationUtils, ensuring:
1. Sensitive data is sanitized based on configured rules
2. Consistent handling of body content across all instrumentation modules
3. Support for both String and byte[] body formats with proper charset handling

## Notes
- The sanitization respects the existing headers when available, allowing content-type based sanitization decisions
- For cases where headers aren't available, the simplified `sanitize(String body)` method is used
- Byte array bodies are properly converted to strings using the specified charset (defaulting to UTF-8 if not provided)
