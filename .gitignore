# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

out/

!gradle-wrapper.jar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Ignore Gradle project-specific cache directory
.gradle

# Ignore Gradle build output directory
build

.idea

.DS_Store

# the ./gradlew printVersion generates this file
# ignore this file because the version is consumed from the git tags only
semantic-build-versioning.gradle
