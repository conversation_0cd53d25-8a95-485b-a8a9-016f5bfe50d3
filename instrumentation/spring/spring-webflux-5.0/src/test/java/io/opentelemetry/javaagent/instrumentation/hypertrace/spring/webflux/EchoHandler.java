/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.spring.webflux;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

@Component
class EchoHandler {
  Mono<ServerResponse> echo(ServerRequest request) {
    return ServerResponse.accepted()
        .contentType(MediaType.APPLICATION_JSON)
        .header(
            SpringWebFluxTestApplication.RESPONSE_HEADER_NAME,
            SpringWebFluxTestApplication.RESPONSE_HEADER_VALUE)
        .body(request.bodyToMono(String.class), String.class);
  }
}
