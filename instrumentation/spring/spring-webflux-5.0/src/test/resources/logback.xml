<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    <layout class="ch.qos.logback.classic.PatternLayout">
      <Pattern>
        %d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
      </Pattern>
    </layout>
  </appender>

  <root level="WARN">
    <appender-ref ref="console"/>
  </root>

  <!-- this is needed because when Spring Boot starts it overrides the debug log level that was
        configured in AgentTestRunner -->
  <logger name="io.opentelemetry" level="debug"/>

</configuration>
