/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6;

import com.google.protobuf.Message;
import java.util.ArrayList;
import java.util.List;
import org.hypertrace.agent.core.instrumentation.buffer.BoundedBuffersFactory;
import org.hypertrace.agent.core.instrumentation.buffer.BoundedCharArrayWriter;
import org.hypertrace.agent.filter.utils.DataSanitizationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Accumulator for gRPC streaming messages that batches messages and performs sanitization once on
 * the concatenated result.
 */
public class GrpcStreamingMessageAccumulator {
  private static final Logger log = LoggerFactory.getLogger(GrpcStreamingMessageAccumulator.class);

  // Use a bounded buffer to prevent memory issues with large streams
  private final BoundedCharArrayWriter buffer;
  private final List<String> messages;
  private boolean isFirstMessage = true;

  public GrpcStreamingMessageAccumulator() {
    // Initialize with a reasonable default size that matches typical gRPC message limits
    this.buffer = BoundedBuffersFactory.createWriter();
    this.messages = new ArrayList<>();
  }

  /**
   * Add a message to the accumulator. Messages are converted to JSON and stored.
   *
   * @param message The protobuf message to accumulate
   */
  public synchronized void addMessage(Object message) {
    if (message instanceof Message) {
      try {
        String jsonMessage = ProtobufMessageConverter.getMessage((Message) message);
        if (jsonMessage != null && !jsonMessage.isEmpty()) {
          messages.add(jsonMessage);

          // Build JSON array format
          if (!isFirstMessage) {
            buffer.write(",");
          } else {
            buffer.write("[");
            isFirstMessage = false;
          }
          buffer.write(jsonMessage);
        }
      } catch (Exception e) {
        log.debug("Failed to convert message to JSON: {}", e.getMessage());
      }
    }
  }

  /**
   * Get the accumulated messages as a sanitized JSON array string. This method performs
   * sanitization once on the entire concatenated result.
   *
   * @return Sanitized JSON array string of all accumulated messages, or null if no messages
   */
  public synchronized String getAccumulatedMessages() {
    if (messages.isEmpty()) {
      return null;
    }

    try {
      // Close the JSON array
      if (!isFirstMessage) {
        buffer.write("]");
      }

      String concatenatedMessages = buffer.toString();

      // Perform sanitization once on the entire payload
      return DataSanitizationUtils.sanitize(concatenatedMessages);
    } catch (Exception e) {
      log.debug("Failed to get accumulated messages: {}", e.getMessage());

      // Fallback: return individual messages as array
      if (messages.size() == 1) {
        return DataSanitizationUtils.sanitize(messages.get(0));
      } else {
        // Build a simple JSON array
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < messages.size(); i++) {
          if (i > 0) {
            sb.append(",");
          }
          sb.append(messages.get(i));
        }
        sb.append("]");
        return DataSanitizationUtils.sanitize(sb.toString());
      }
    }
  }
}
