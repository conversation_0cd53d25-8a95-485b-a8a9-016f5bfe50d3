/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.server;

import io.grpc.ForwardingServerCall;
import io.grpc.ForwardingServerCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.Status;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcInstrumentationName;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcSpanDecorator;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcStreamingMessageAccumulator;
import java.util.Map;
import org.hypertrace.agent.core.config.InstrumentationConfig;
import org.hypertrace.agent.core.filter.FilterResult;
import org.hypertrace.agent.core.instrumentation.HypertraceSemanticAttributes;
import org.hypertrace.agent.filter.FilterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GrpcServerInterceptor implements ServerInterceptor {

  private static final Logger log = LoggerFactory.getLogger(GrpcServerInterceptor.class);

  @Override
  public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
      ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {

    try {
      InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
      if (!instrumentationConfig.isInstrumentationEnabled(
          GrpcInstrumentationName.PRIMARY, GrpcInstrumentationName.OTHER)) {
        return next.startCall(call, headers);
      }

      Span currentSpan = Span.current();

      Map<String, String> mapHeaders = GrpcSpanDecorator.metadataToMap(headers);

      if (instrumentationConfig.rpcMetadata().request()) {
        GrpcSpanDecorator.addMetadataAttributes(mapHeaders, currentSpan);
      }

      FilterResult filterResult =
          FilterRegistry.getFilter().evaluateRequestHeaders(currentSpan, mapHeaders);
      if (filterResult.shouldBlock()) {
        // map http codes with grpc codes
        // We cannot send custom message in grpc calls
        call.close(mapHttpToGrpcStatus(filterResult.getBlockingStatusCode()), new Metadata());
        @SuppressWarnings("unchecked")
        ServerCall.Listener<ReqT> noop = NoopServerCallListener.INSTANCE;
        return noop;
      }

      Listener<ReqT> serverCall =
          next.startCall(
              new TracingServerCall<>(call, currentSpan, call.getMethodDescriptor()), headers);
      return new TracingServerCallListener<>(serverCall, currentSpan, call.getMethodDescriptor());
    } catch (Throwable t) {
      log.debug("exception thrown during intercepting server call", t);
      return next.startCall(call, headers);
    }
  }

  /**
   * Mapping according to https://github.com/grpc/grpc/blob/master/doc/http-grpc-status-mapping.md
   */
  private static Status mapHttpToGrpcStatus(int httpStatus) {
    switch (httpStatus) {
      case 400:
        return Status.INTERNAL;
      case 401:
        return Status.UNAUTHENTICATED;
      case 403:
        return Status.PERMISSION_DENIED;
      case 404:
        return Status.UNIMPLEMENTED;
      case 429:
        return Status.UNAVAILABLE;
      case 502:
        return Status.UNAVAILABLE;
      case 503:
        return Status.UNAVAILABLE;
      case 504:
        return Status.UNAVAILABLE;
      default:
        return Status.UNKNOWN;
    }
  }

  static final class TracingServerCall<ReqT, RespT>
      extends ForwardingServerCall.SimpleForwardingServerCall<ReqT, RespT> {

    private final Span span;
    private final MethodDescriptor<ReqT, RespT> method;
    private final GrpcStreamingMessageAccumulator responseAccumulator;

    TracingServerCall(
        ServerCall<ReqT, RespT> delegate, Span span, MethodDescriptor<ReqT, RespT> method) {
      super(delegate);
      this.span = span;
      this.method = method;
      this.responseAccumulator = new GrpcStreamingMessageAccumulator();
    }

    @Override
    public void sendMessage(RespT message) {
      super.sendMessage(message);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().response()) {
          if (isStreamingMethod(method)) {
            // For streaming calls, accumulate messages
            responseAccumulator.addMessage(message);
          } else {
            // For unary calls, add immediately with sanitization
            GrpcSpanDecorator.addMessageAttribute(
                message, span, HypertraceSemanticAttributes.RPC_RESPONSE_BODY);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc server response body", t);
      }
    }

    @Override
    public void close(Status status, Metadata trailers) {
      // For server streaming or bidirectional streaming, finalize response messages
      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().response()
            && (method.getType() == MethodDescriptor.MethodType.SERVER_STREAMING
                || method.getType() == MethodDescriptor.MethodType.BIDI_STREAMING)) {
          String accumulatedMessages = responseAccumulator.getAccumulatedMessages();
          if (accumulatedMessages != null && !accumulatedMessages.isEmpty()) {
            span.setAttribute(HypertraceSemanticAttributes.RPC_RESPONSE_BODY, accumulatedMessages);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while finalizing grpc server response body", t);
      }
      super.close(status, trailers);
    }

    private static boolean isStreamingMethod(MethodDescriptor<?, ?> method) {
      return method.getType() != MethodDescriptor.MethodType.UNARY;
    }

    @Override
    public void sendHeaders(Metadata headers) {
      super.sendHeaders(headers);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcMetadata().response()) {
          GrpcSpanDecorator.addMetadataAttributes(
              headers, span, HypertraceSemanticAttributes::rpcResponseMetadata);
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc server response headers", t);
      }
    }
  }

  static final class TracingServerCallListener<ReqT>
      extends ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT> {

    private final Span span;
    private final MethodDescriptor<ReqT, ?> method;
    private final GrpcStreamingMessageAccumulator requestAccumulator;

    TracingServerCallListener(
        Listener<ReqT> delegate, Span span, MethodDescriptor<ReqT, ?> method) {
      super(delegate);
      this.span = span;
      this.method = method;
      this.requestAccumulator = new GrpcStreamingMessageAccumulator();
    }

    @Override
    public void onMessage(ReqT message) {
      delegate().onMessage(message);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().request()) {
          if (isStreamingMethod(method)) {
            // For streaming calls, accumulate messages
            requestAccumulator.addMessage(message);
          } else {
            // For unary calls, add immediately with sanitization
            GrpcSpanDecorator.addMessageAttribute(
                message, span, HypertraceSemanticAttributes.RPC_REQUEST_BODY);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc server request body", t);
      }
    }

    @Override
    public void onHalfClose() {
      // For client streaming or bidirectional streaming, finalize request messages
      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().request()
            && (method.getType() == MethodDescriptor.MethodType.CLIENT_STREAMING
                || method.getType() == MethodDescriptor.MethodType.BIDI_STREAMING)) {
          String accumulatedMessages = requestAccumulator.getAccumulatedMessages();
          if (accumulatedMessages != null && !accumulatedMessages.isEmpty()) {
            span.setAttribute(HypertraceSemanticAttributes.RPC_REQUEST_BODY, accumulatedMessages);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while finalizing grpc server request body", t);
      }
      super.onHalfClose();
    }

    private static boolean isStreamingMethod(MethodDescriptor<?, ?> method) {
      return method.getType() != MethodDescriptor.MethodType.UNARY;
    }
  }
}
