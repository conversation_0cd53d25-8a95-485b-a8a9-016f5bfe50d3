/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.client;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientCall.Listener;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.Status;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcInstrumentationName;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcSpanDecorator;
import io.opentelemetry.javaagent.instrumentation.hypertrace.grpc.v1_6.GrpcStreamingMessageAccumulator;
import org.hypertrace.agent.core.config.InstrumentationConfig;
import org.hypertrace.agent.core.instrumentation.HypertraceSemanticAttributes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GrpcClientInterceptor implements ClientInterceptor {

  private static final Logger log = LoggerFactory.getLogger(GrpcClientInterceptor.class);

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
      MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {

    try {
      InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
      if (!instrumentationConfig.isInstrumentationEnabled(
          GrpcInstrumentationName.PRIMARY, GrpcInstrumentationName.OTHER)) {
        return next.newCall(method, callOptions);
      }

      Span currentSpan = Span.current();
      ClientCall<ReqT, RespT> clientCall = next.newCall(method, callOptions);
      return new GrpcClientInterceptor.TracingClientCall<>(clientCall, currentSpan, method);
    } catch (Throwable t) {
      log.debug("exception thrown while intercepting grpc client call", t);
      return next.newCall(method, callOptions);
    }
  }

  static final class TracingClientCall<ReqT, RespT>
      extends ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT> {

    private final Span span;
    private final MethodDescriptor<ReqT, RespT> method;
    private final GrpcStreamingMessageAccumulator requestAccumulator;
    private final GrpcStreamingMessageAccumulator responseAccumulator;

    TracingClientCall(
        ClientCall<ReqT, RespT> delegate, Span span, MethodDescriptor<ReqT, RespT> method) {
      super(delegate);
      this.span = span;
      this.method = method;
      this.requestAccumulator = new GrpcStreamingMessageAccumulator();
      this.responseAccumulator = new GrpcStreamingMessageAccumulator();
    }

    @Override
    public void start(Listener<RespT> responseListener, Metadata headers) {
      super.start(
          new TracingClientCallListener<>(responseListener, span, method, responseAccumulator),
          headers);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcMetadata().request()) {
          GrpcSpanDecorator.addMetadataAttributes(
              headers, span, HypertraceSemanticAttributes::rpcRequestMetadata);
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc client request metadata", t);
      }
    }

    @Override
    public void sendMessage(ReqT message) {
      super.sendMessage(message);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().request()) {
          if (isStreamingMethod(method)) {
            // For streaming calls, accumulate messages
            requestAccumulator.addMessage(message);
          } else {
            // For unary calls, add immediately with sanitization
            GrpcSpanDecorator.addMessageAttribute(
                message, span, HypertraceSemanticAttributes.RPC_REQUEST_BODY);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc client request body", t);
      }
    }

    @Override
    public void halfClose() {
      // For client streaming or bidirectional streaming, finalize request messages
      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().request()
            && (method.getType() == MethodDescriptor.MethodType.CLIENT_STREAMING
                || method.getType() == MethodDescriptor.MethodType.BIDI_STREAMING)) {
          String accumulatedMessages = requestAccumulator.getAccumulatedMessages();
          if (accumulatedMessages != null && !accumulatedMessages.isEmpty()) {
            span.setAttribute(HypertraceSemanticAttributes.RPC_REQUEST_BODY, accumulatedMessages);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while finalizing grpc client request body", t);
      }
      super.halfClose();
    }

    private static boolean isStreamingMethod(MethodDescriptor<?, ?> method) {
      return method.getType() != MethodDescriptor.MethodType.UNARY;
    }
  }

  static final class TracingClientCallListener<RespT>
      extends ForwardingClientCallListener.SimpleForwardingClientCallListener<RespT> {
    private final Span span;
    private final MethodDescriptor<?, RespT> method;
    private final GrpcStreamingMessageAccumulator responseAccumulator;

    TracingClientCallListener(
        Listener<RespT> delegate,
        Span span,
        MethodDescriptor<?, RespT> method,
        GrpcStreamingMessageAccumulator responseAccumulator) {
      super(delegate);
      this.span = span;
      this.method = method;
      this.responseAccumulator = responseAccumulator;
    }

    @Override
    public void onMessage(RespT message) {
      delegate().onMessage(message);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().response()) {
          if (isStreamingMethod(method)) {
            // For streaming calls, accumulate messages
            responseAccumulator.addMessage(message);
          } else {
            // For unary calls, add immediately with sanitization
            GrpcSpanDecorator.addMessageAttribute(
                message, span, HypertraceSemanticAttributes.RPC_RESPONSE_BODY);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc client response body", t);
      }
    }

    @Override
    public void onHeaders(Metadata headers) {
      super.onHeaders(headers);

      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcMetadata().response()) {
          GrpcSpanDecorator.addMetadataAttributes(
              headers, span, HypertraceSemanticAttributes::rpcResponseMetadata);
        }
      } catch (Throwable t) {
        log.debug("exception thrown while capturing grpc client response metadata", t);
      }
    }

    @Override
    public void onClose(Status status, Metadata trailers) {
      // For server streaming or bidirectional streaming, finalize response messages
      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig.rpcBody().response()
            && (method.getType() == MethodDescriptor.MethodType.SERVER_STREAMING
                || method.getType() == MethodDescriptor.MethodType.BIDI_STREAMING)) {
          String accumulatedMessages = responseAccumulator.getAccumulatedMessages();
          if (accumulatedMessages != null && !accumulatedMessages.isEmpty()) {
            span.setAttribute(HypertraceSemanticAttributes.RPC_RESPONSE_BODY, accumulatedMessages);
          }
        }
      } catch (Throwable t) {
        log.debug("exception thrown while finalizing grpc client response body", t);
      }
      super.onClose(status, trailers);
    }

    private static boolean isStreamingMethod(MethodDescriptor<?, ?> method) {
      return method.getType() != MethodDescriptor.MethodType.UNARY;
    }
  }
}
