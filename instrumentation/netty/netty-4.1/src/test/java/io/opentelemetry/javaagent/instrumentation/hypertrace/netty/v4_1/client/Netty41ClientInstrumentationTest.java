/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.netty.v4_1.client;

import io.opentelemetry.proto.trace.v1.Span;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.asynchttpclient.AsyncCompletionHandler;
import org.asynchttpclient.AsyncHttpClient;
import org.asynchttpclient.BoundRequestBuilder;
import org.asynchttpclient.DefaultAsyncHttpClientConfig;
import org.asynchttpclient.Dsl;
import org.asynchttpclient.ListenableFuture;
import org.hypertrace.agent.testing.AbstractHttpClientTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class Netty41ClientInstrumentationTest extends AbstractHttpClientTest {

  private final DefaultAsyncHttpClientConfig clientConfig =
      new DefaultAsyncHttpClientConfig.Builder().setRequestTimeout(30000).build();
  private final AsyncHttpClient asyncHttpClient = Dsl.asyncHttpClient(clientConfig);

  public Netty41ClientInstrumentationTest() {
    super(false);
  }

  @Override
  public Response doPostRequest(
      String uri, Map<String, String> headers, String body, String contentType)
      throws ExecutionException, InterruptedException {

    ByteArrayInputStream inputStream = new ByteArrayInputStream(body.getBytes());
    BoundRequestBuilder requestBuilder = asyncHttpClient.preparePost(uri).setBody(inputStream);

    for (Map.Entry<String, String> entry : headers.entrySet()) {
      requestBuilder = requestBuilder.addHeader(entry.getKey(), entry.getValue());
    }

    requestBuilder = requestBuilder.addHeader("Content-Type", contentType);
    ListenableFuture<Response> response =
        requestBuilder.execute(
            new AsyncCompletionHandler<Response>() {
              @Override
              public Response onCompleted(org.asynchttpclient.Response response) {
                return new Response(
                    response.hasResponseBody() ? response.getResponseBody() : null,
                    response.getStatusCode());
              }
            });

    // wait for the result
    return response.get();
  }

  @Override
  public Response doGetRequest(String uri, Map<String, String> headers)
      throws ExecutionException, InterruptedException {

    BoundRequestBuilder requestBuilder = asyncHttpClient.prepareGet(uri);

    for (Map.Entry<String, String> entry : headers.entrySet()) {
      requestBuilder = requestBuilder.addHeader(entry.getKey(), entry.getValue());
    }

    ListenableFuture<Response> response =
        requestBuilder.execute(
            new AsyncCompletionHandler<Response>() {
              @Override
              public Response onCompleted(org.asynchttpclient.Response response) {
                return new Response(
                    response.hasResponseBody() ? response.getResponseBody() : null,
                    response.getStatusCode());
              }
            });

    // wait for the result
    return response.get();
  }

  @Test
  public void testJsonRequestWithPIISanitization() throws Exception {
    String body = "{\"username\":\"<EMAIL>\",\"password\":\"123456\",\"api_key\":\"sk_test_**********\"}";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/json");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces = TEST_WRITER.waitForSpans(1, 
        span -> !span.getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)
            || span.getAttributesList().stream()
                .noneMatch(
                    keyValue ->
                        keyValue.getKey().equals("http.url")
                            && keyValue.getValue().getStringValue().contains("/echo")));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(1, traces.get(0).size());
    Span clientSpan = traces.get(0).get(0);

    // Verify request body is sanitized
    String requestBody = TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("\"username\":\"****@example.com\""));
    Assertions.assertTrue(requestBody.contains("\"password\":\"****\""));
    Assertions.assertTrue(requestBody.contains("\"api_key\":\"****\""));

    // Verify response body is sanitized
    String responseBodyStr = TEST_WRITER.getAttributesMap(clientSpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("\"username\":\"****@example.com\""));
    Assertions.assertTrue(responseBodyStr.contains("\"password\":\"****\""));
    Assertions.assertTrue(responseBodyStr.contains("\"api_key\":\"****\""));
  }

  @Test
  public void testFormEncodedRequestWithPIISanitization() throws Exception {
    String body = "username=<EMAIL>&password=secret123&credit_card=****************&ssn=***********";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/x-www-form-urlencoded");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces = TEST_WRITER.waitForSpans(1, 
        span -> !span.getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)
            || span.getAttributesList().stream()
                .noneMatch(
                    keyValue ->
                        keyValue.getKey().equals("http.url")
                            && keyValue.getValue().getStringValue().contains("/echo")));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(1, traces.get(0).size());
    Span clientSpan = traces.get(0).get(0);

    // Verify request body is sanitized
    String requestBody = TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("username=****@example.com"));
    Assertions.assertTrue(requestBody.contains("password=****"));
    Assertions.assertTrue(requestBody.contains("credit_card=****"));
    Assertions.assertTrue(requestBody.contains("ssn=****"));

    // Verify response body is sanitized
    String responseBodyStr = TEST_WRITER.getAttributesMap(clientSpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("username=****@example.com"));
    Assertions.assertTrue(responseBodyStr.contains("password=****"));
    Assertions.assertTrue(responseBodyStr.contains("credit_card=****"));
    Assertions.assertTrue(responseBodyStr.contains("ssn=****"));
  }

  @Test
  public void testXmlRequestWithPIISanitization() throws Exception {
    String body = "<user><email><EMAIL></email><password>secret123</password><account_number>**********</account_number></user>";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/xml");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces = TEST_WRITER.waitForSpans(1, 
        span -> !span.getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)
            || span.getAttributesList().stream()
                .noneMatch(
                    keyValue ->
                        keyValue.getKey().equals("http.url")
                            && keyValue.getValue().getStringValue().contains("/echo")));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(1, traces.get(0).size());
    Span clientSpan = traces.get(0).get(0);

    // Verify request body is sanitized
    String requestBody = TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("<email>****@example.com</email>"));
    Assertions.assertTrue(requestBody.contains("<password>****</password>"));
    Assertions.assertTrue(requestBody.contains("<account_number>****</account_number>"));

    // Verify response body is sanitized
    String responseBodyStr = TEST_WRITER.getAttributesMap(clientSpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("<email>****@example.com</email>"));
    Assertions.assertTrue(responseBodyStr.contains("<password>****</password>"));
    Assertions.assertTrue(responseBodyStr.contains("<account_number>****</account_number>"));
  }
}
