/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.apachehttpclient.v4_0;

import io.opentelemetry.proto.trace.v1.Span;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.hypertrace.agent.testing.AbstractHttpClientTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ApacheHttpClientInstrumentationTest extends AbstractHttpClientTest {

  private final HttpClient client = new DefaultHttpClient();

  public ApacheHttpClientInstrumentationTest() {
    super(true);
  }

  @Override
  public Response doPostRequest(
      String uri, Map<String, String> headers, String body, String contentType) throws IOException {

    HttpPost request = new HttpPost();
    for (Map.Entry<String, String> entry : headers.entrySet()) {
      request.addHeader(entry.getKey(), entry.getValue());
    }
    request.setURI(URI.create(uri));
    StringEntity entity = new StringEntity(body);
    entity.setContentType(contentType);
    request.setEntity(entity);
    request.addHeader("Content-type", contentType);
    HttpResponse response = client.execute(request);
    InputStream inputStream = response.getEntity().getContent();
    return new Response(readInputStream(inputStream), response.getStatusLine().getStatusCode());
  }

  @Override
  public Response doGetRequest(String uri, Map<String, String> headers) throws IOException {
    HttpGet request = new HttpGet();
    for (Map.Entry<String, String> entry : headers.entrySet()) {
      request.addHeader(entry.getKey(), entry.getValue());
    }
    request.setURI(URI.create(uri));
    HttpResponse response = client.execute(request);
    if (response.getEntity() == null || response.getEntity().getContentLength() <= 0) {
      return new Response(null, response.getStatusLine().getStatusCode());
    }
    InputStream inputStream = response.getEntity().getContent();
    return new Response(readInputStream(inputStream), response.getStatusLine().getStatusCode());
  }

  @Test
  public void testJsonRequestWithPIISanitization() throws Exception {
    String body =
        "{\"username\":\"<EMAIL>\",\"password\":\"123456\",\"api_key\":\"sk_test_**********\"}";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/json");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces =
        TEST_WRITER.waitForSpans(2, span -> span.getKind().equals(Span.SpanKind.SPAN_KIND_SERVER));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(2, traces.get(0).size());

    Span clientSpan = traces.get(0).get(0);
    Span responseBodySpan = traces.get(0).get(1);
    if (traces.get(0).get(1).getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)) {
      clientSpan = traces.get(0).get(1);
      responseBodySpan = traces.get(0).get(0);
    }

    // Verify request body is sanitized
    String requestBody =
        TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("\"username\":\"****@example.com\""));
    Assertions.assertTrue(requestBody.contains("\"password\":\"****\""));
    Assertions.assertTrue(requestBody.contains("\"api_key\":\"****\""));

    // Verify response body is sanitized
    String responseBodyStr =
        TEST_WRITER.getAttributesMap(responseBodySpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("\"username\":\"****@example.com\""));
    Assertions.assertTrue(responseBodyStr.contains("\"password\":\"****\""));
    Assertions.assertTrue(responseBodyStr.contains("\"api_key\":\"****\""));
  }

  @Test
  public void testFormEncodedRequestWithPIISanitization() throws Exception {
    String body =
        "username=<EMAIL>&password=secret123&credit_card=****************&ssn=***********";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/x-www-form-urlencoded");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces =
        TEST_WRITER.waitForSpans(2, span -> span.getKind().equals(Span.SpanKind.SPAN_KIND_SERVER));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(2, traces.get(0).size());

    Span clientSpan = traces.get(0).get(0);
    Span responseBodySpan = traces.get(0).get(1);
    if (traces.get(0).get(1).getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)) {
      clientSpan = traces.get(0).get(1);
      responseBodySpan = traces.get(0).get(0);
    }

    // Verify request body is sanitized
    String requestBody =
        TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("username=****@example.com"));
    Assertions.assertTrue(requestBody.contains("password=****"));
    Assertions.assertTrue(requestBody.contains("credit_card=****"));
    Assertions.assertTrue(requestBody.contains("ssn=****"));

    // Verify response body is sanitized
    String responseBodyStr =
        TEST_WRITER.getAttributesMap(responseBodySpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("username=****@example.com"));
    Assertions.assertTrue(responseBodyStr.contains("password=****"));
    Assertions.assertTrue(responseBodyStr.contains("credit_card=****"));
    Assertions.assertTrue(responseBodyStr.contains("ssn=****"));
  }

  @Test
  public void testXmlRequestWithPIISanitization() throws Exception {
    String body =
        "<user><email><EMAIL></email><password>secret123</password><account_number>**********</account_number></user>";
    String uri = String.format("http://localhost:%d/echo", testHttpServer.port());
    Map<String, String> headers = new HashMap<>();
    headers.put("test-header", "test-value");

    Response response = doPostRequest(uri, headers, body, "application/xml");

    Assertions.assertEquals(200, response.statusCode);
    Assertions.assertEquals(body, response.body);

    TEST_WRITER.waitForTraces(1);
    List<List<Span>> traces =
        TEST_WRITER.waitForSpans(2, span -> span.getKind().equals(Span.SpanKind.SPAN_KIND_SERVER));

    Assertions.assertEquals(1, traces.size());
    Assertions.assertEquals(2, traces.get(0).size());

    Span clientSpan = traces.get(0).get(0);
    Span responseBodySpan = traces.get(0).get(1);
    if (traces.get(0).get(1).getKind().equals(Span.SpanKind.SPAN_KIND_CLIENT)) {
      clientSpan = traces.get(0).get(1);
      responseBodySpan = traces.get(0).get(0);
    }

    // Verify request body is sanitized
    String requestBody =
        TEST_WRITER.getAttributesMap(clientSpan).get("http.request.body").getStringValue();
    Assertions.assertTrue(requestBody.contains("<email>****@example.com</email>"));
    Assertions.assertTrue(requestBody.contains("<password>****</password>"));
    Assertions.assertTrue(requestBody.contains("<account_number>****</account_number>"));

    // Verify response body is sanitized
    String responseBodyStr =
        TEST_WRITER.getAttributesMap(responseBodySpan).get("http.response.body").getStringValue();
    Assertions.assertTrue(responseBodyStr.contains("<email>****@example.com</email>"));
    Assertions.assertTrue(responseBodyStr.contains("<password>****</password>"));
    Assertions.assertTrue(responseBodyStr.contains("<account_number>****</account_number>"));
  }
}
