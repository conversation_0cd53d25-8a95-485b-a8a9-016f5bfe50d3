/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.opentelemetry.javaagent.instrumentation.hypertrace.kafka.v2_6.consumer;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;
import static net.bytebuddy.matcher.ElementMatchers.isMethod;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.hypertrace.agent.core.config.InstrumentationConfig;
import org.hypertrace.agent.core.instrumentation.HypertraceSemanticAttributes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KafkaConsumerInstrumentation implements TypeInstrumentation {

  @Override
  public ElementMatcher<ClassLoader> classLoaderOptimization() {
    return hasClassesNamed("org.apache.kafka.clients.consumer.KafkaConsumer");
  }

  @Override
  public ElementMatcher<TypeDescription> typeMatcher() {
    return named("org.apache.kafka.clients.consumer.KafkaConsumer");
  }

  @Override
  public void transform(TypeTransformer transformer) {
    transformer.applyAdviceToMethod(
        isMethod()
            .and(isPublic())
            .and(named("poll"))
            .and(returns(named("org.apache.kafka.clients.consumer.ConsumerRecords"))),
        KafkaConsumerInstrumentation.class.getName() + "$PollAdvice");
  }

  public static class PollAdvice {
    private static final Logger log = LoggerFactory.getLogger(PollAdvice.class);

    @Advice.OnMethodExit(suppress = Throwable.class)
    public static void onExit(@Advice.Return ConsumerRecords<?, ?> records) {
      try {
        InstrumentationConfig instrumentationConfig = InstrumentationConfig.ConfigProvider.get();
        if (instrumentationConfig != null && instrumentationConfig.kafkaBody().response()) {
          Span currentSpan = Span.current();
          if (currentSpan.isRecording() && records != null && !records.isEmpty()) {
            StringBuilder messageBodyBuilder = new StringBuilder();
            boolean first = true;

            for (ConsumerRecord<?, ?> record : records) {
              if (!first) {
                messageBodyBuilder.append(", ");
              }
              first = false;

              Object value = record.value();
              if (value != null) {
                messageBodyBuilder.append(value.toString());
              }
            }

            if (messageBodyBuilder.length() > 0) {
              currentSpan.setAttribute(
                  HypertraceSemanticAttributes.KAFKA_CONSUMER_MESSAGE_BODY,
                  messageBodyBuilder.toString());
            }
          }
        }
      } catch (Throwable t) {
        log.debug("Failed to capture Kafka consumer message body", t);
      }
    }
  }
}
