plugins {
    `java-library`
    id("net.bytebuddy.byte-buddy")
    id("io.opentelemetry.instrumentation.auto-instrumentation")
    muzzle
}
evaluationDependsOn(":javaagent-tooling")

muzzle {
    pass {
        group = "org.apache.kafka"
        module = "kafka-clients"
        versions = "[2.6.0,)"
        assertInverse = true
    }
}

afterEvaluate{
    io.opentelemetry.instrumentation.gradle.bytebuddy.ByteBuddyPluginConfigurator(project,
            sourceSets.main.get(),
            io.opentelemetry.javaagent.tooling.muzzle.generation.MuzzleCodeGenerationPlugin::class.java.name,
        files(project(":javaagent-tooling").configurations["instrumentationMuzzle"], configurations.runtimeClasspath)
    ).configure()
}

val versions: Map<String, String> by extra

dependencies {
    api(project(":javaagent-core"))
    
    compileOnly("org.apache.kafka:kafka-clients:2.6.0")
    
    testImplementation(project(":testing-common"))
    testImplementation("org.apache.kafka:kafka-clients:2.6.0")
    testImplementation("org.testcontainers:kafka:1.15.2")
}
