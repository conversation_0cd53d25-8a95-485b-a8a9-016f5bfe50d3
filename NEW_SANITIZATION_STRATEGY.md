# 🔒 新的差异化脱敏策略

## 📋 策略概述

根据数据敏感性和业务需求，我们实现了差异化的脱敏策略：

### 🔐 完全掩码策略（高敏感数据）
**适用于**: 密码、密钥、令牌等绝对敏感信息
**策略**: 完全使用 `*` 掩码，不保留任何原始信息

### 👤 部分掩码策略（中等敏感数据）  
**适用于**: 用户名、邮箱、电话、信用卡等需要部分识别的信息
**策略**: 保留部分字符用于识别，其余用 `*` 掩码

## 🎯 具体脱敏规则

### 1. 密码类字段 - 完全掩码
```json
{
  "id": "password-fields",
  "maskValue": "********",
  "fieldNames": [
    "password", "passwd", "pwd", "secret", "token",
    "apiKey", "api_key", "accessToken", "refreshToken"
  ]
}
```

**示例**:
- `"password": "mySecret123"` → `"password": "********"`
- `"apiKey": "sk-1234567890abcdef"` → `"apiKey": "********"`

### 2. 邮箱地址 - 保留前2字符和域名
```json
{
  "id": "email-pattern",
  "pattern": "([a-zA-Z0-9._%+-]{1,2})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})",
  "maskValue": "$1***@$2"
}
```

**示例**:
- `<EMAIL>` → `jo***@example.com`
- `<EMAIL>` → `al***@company.org`

### 3. 信用卡号 - 只显示后4位
```json
{
  "id": "credit-card",
  "pattern": "\\b(\\d{4})[-\\s]?(\\d{4})[-\\s]?(\\d{4})[-\\s]?(\\d{4})\\b",
  "maskValue": "****-****-****-$4"
}
```

**示例**:
- `4111-1111-1111-1234` → `****-****-****-1234`
- `4111 1111 1111 5678` → `****-****-****-5678`

### 4. 电话号码 - 只显示后4位
```json
{
  "id": "phone-numbers", 
  "pattern": "(\\+?1?[-\\.\\s]?\\(?[0-9]{3}\\)?[-\\.\\s]?[0-9]{3})[-\\.\\s]?([0-9]{4})",
  "maskValue": "***-***-$2"
}
```

**示例**:
- `******-123-4567` → `***-***-4567`
- `(*************` → `***-***-4567`

### 5. 用户名 - 部分掩码（待实现）
```json
{
  "id": "username-fields",
  "maskValue": "PARTIAL_MASK",
  "fieldNames": ["username", "userName", "loginName"]
}
```

**预期效果**:
- `"username": "johnsmith"` → `"username": "jo****th"`
- `"userName": "alice"` → `"userName": "al**e"`

## 📊 脱敏效果对比

### 修改前（统一掩码）
```json
{
  "username": "johnsmith",
  "password": "secret123",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "creditCard": "4111-1111-1111-1234"
}
```
↓ 脱敏后
```json
{
  "username": "****",
  "password": "****", 
  "email": "****@****.***",
  "phone": "***-***-****",
  "creditCard": "****-****-****-****"
}
```

### 修改后（差异化掩码）
```json
{
  "username": "johnsmith",
  "password": "secret123", 
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "creditCard": "4111-1111-1111-1234"
}
```
↓ 脱敏后
```json
{
  "username": "jo****th",
  "password": "********",
  "email": "jo***@example.com", 
  "phone": "***-***-4567",
  "creditCard": "****-****-****-1234"
}
```

## 🎯 业务价值

### 1. 安全性提升
- **绝对敏感数据**: 完全掩码，零信息泄露
- **相对敏感数据**: 部分掩码，平衡安全与可用性

### 2. 可用性改善
- **故障排查**: 保留部分信息便于问题定位
- **数据关联**: 可以通过部分信息关联用户行为
- **业务分析**: 支持基本的数据分析需求

### 3. 合规要求
- **GDPR**: 满足个人数据保护要求
- **PCI DSS**: 信用卡信息符合支付卡行业标准
- **内部审计**: 满足企业数据安全政策

## 🔧 配置管理

### 启用新策略
```bash
# 重新加载配置
curl -X POST http://localhost:8081/api/sanitization/rules/reload

# 验证配置
curl http://localhost:8081/api/sanitization/rules | jq '.rules[] | {id, maskValue}'
```

### 动态调整
可以通过修改 `rules.json` 文件并重新加载来调整脱敏策略：

1. **调整掩码长度**: 修改 `maskValue` 中的 `*` 数量
2. **调整保留字符**: 修改正则表达式的捕获组
3. **添加新字段**: 在 `fieldNames` 中添加新的敏感字段

## 🧪 测试验证

### 测试数据
```json
{
  "user": {
    "username": "johnsmith",
    "password": "mySecret123",
    "email": "<EMAIL>",
    "profile": {
      "phone": "******-123-4567",
      "creditCard": "4111-1111-1111-1234",
      "ssn": "***********"
    }
  },
  "auth": {
    "apiKey": "sk-1234567890abcdef",
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "rt_1234567890abcdef"
  }
}
```

### 预期脱敏结果
```json
{
  "user": {
    "username": "jo****th",
    "password": "********",
    "email": "jo***@company.com",
    "profile": {
      "phone": "***-***-4567", 
      "creditCard": "****-****-****-1234",
      "ssn": "***-**-****"
    }
  },
  "auth": {
    "apiKey": "********",
    "accessToken": "********",
    "refreshToken": "********"
  }
}
```

## 📈 下一步优化

1. **智能长度调整**: 根据原始数据长度动态调整掩码长度
2. **上下文感知**: 根据请求上下文调整脱敏强度
3. **用户权限**: 根据用户角色提供不同级别的脱敏
4. **性能优化**: 缓存编译后的正则表达式模式

这种差异化的脱敏策略在保护敏感数据的同时，最大化地保留了数据的可用性和业务价值！
