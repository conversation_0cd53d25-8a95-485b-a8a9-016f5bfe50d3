# 敏感数据脱敏功能

## 概述

Hypertrace Java Agent 现在支持对追踪的请求体和响应体进行敏感数据脱敏。这个功能可以自动识别并掩盖常见的敏感数据，如密码、API密钥、信用卡号、社会安全号码等。

## 功能特性

### 支持的敏感数据类型

1. **敏感字段名称**（不区分大小写）：
   - 密码相关：`password`, `passwd`, `pwd`, `pass`
   - 密钥相关：`secret`, `token`, `apikey`, `api_key`, `api-key`
   - 认证相关：`authorization`, `auth`
   - 支付相关：`credit_card`, `creditcard`, `card_number`, `cardnumber`, `cvv`, `cvc`
   - 个人信息：`ssn`, `social_security_number`, `email`, `phone`, `mobile`
   - 账户相关：`account_number`, `accountnumber`, `pin`, `passcode`

2. **模式匹配**：
   - 邮箱地址：保留域名部分，掩盖用户名部分
   - 信用卡号：13-19位数字
   - 社会安全号码：XXX-XX-XXXX 或 9位数字
   - 电话号码：美国格式电话号码
   - API密钥：20位以上的字母数字串

### 支持的内容类型

- `application/json`
- `application/xml`
- `text/xml`
- `application/x-www-form-urlencoded`
- `text/plain`

## 配置方法

### 1. 启用/禁用数据脱敏

默认情况下，数据脱敏功能是**启用**的。您可以通过以下方式控制：

**通过系统属性：**
```bash
java -Dht.data.sanitization.enabled=false -javaagent:hypertrace-agent.jar YourApp
```

**通过环境变量：**
```bash
export HT_DATA_SANITIZATION_ENABLED=false
java -javaagent:hypertrace-agent.jar YourApp
```

### 2. 配置数据捕获

确保在配置文件中启用了数据捕获功能：

```yaml
service_name: your-service
reporting:
  endpoint: http://localhost:4317

data_capture:
  http_body:
    request: true
    response: true
  allowed_content_types:
    - application/json
    - application/xml
    - text/xml
    - application/x-www-form-urlencoded
    - text/plain
```

## 示例

### JSON 请求体脱敏示例

**原始请求：**
```json
{
  "username": "<EMAIL>",
  "password": "secretPassword123",
  "credit_card": "4111-1111-1111-1111",
  "phone": "************",
  "api_key": "sk_test_AbCdEfGhIjKlMnOpQrStUvWx"
}
```

**脱敏后：**
```json
{
  "username": "****@example.com",
  "password": "****",
  "credit_card": "****",
  "phone": "****",
  "api_key": "****"
}
```

### Form Data 脱敏示例

**原始请求：**
```
username=<EMAIL>&password=secret123&token=abc123xyz
```

**脱敏后：**
```
username=****@example.com&password=****&token=****
```

### XML 脱敏示例

**原始请求：**
```xml
<user>
  <email><EMAIL></email>
  <password>secret123</password>
  <ssn>***********</ssn>
</user>
```

**脱敏后：**
```xml
<user>
  <email>****@example.com</email>
  <password>****</password>
  <ssn>****</ssn>
</user>
```

## 扩展和自定义

### 创建自定义 Sanitizer

如果需要自定义脱敏规则，可以实现 `SensitiveDataSanitizer` 接口：

```java
public class CustomSanitizer implements SensitiveDataSanitizer {
  
  @Override
  public String sanitize(String input, Map<String, String> headers) {
    // 实现自定义脱敏逻辑
    return input;
  }
  
  @Override
  public boolean shouldSanitize(String contentType) {
    // 决定是否需要脱敏
    return true;
  }
}
```

### 在 Instrumentation 中使用

在自定义的 instrumentation 中使用数据脱敏工具：

```java
import org.hypertrace.agent.core.instrumentation.utils.DataSanitizationUtils;

// 添加脱敏的属性到 span
DataSanitizationUtils.addSanitizedAttribute(
    span, 
    HypertraceSemanticAttributes.HTTP_REQUEST_BODY,
    bodyContent,
    headers
);

// 或者直接脱敏字符串
String sanitized = DataSanitizationUtils.sanitize(bodyContent, headers);
```

## 性能考虑

1. 脱敏处理会增加少量的性能开销，特别是对于大型 JSON/XML 文档
2. 建议只在必要时启用数据捕获和脱敏
3. 可以通过配置 `allowed_content_types` 来限制需要处理的内容类型

## 安全建议

1. 即使启用了脱敏，也要谨慎处理敏感数据
2. 定期审查脱敏规则，确保覆盖所有敏感字段
3. 在生产环境中，考虑完全禁用某些特别敏感的数据捕获
4. 使用 HTTPS/TLS 传输追踪数据到收集端点

## 故障排除

如果脱敏功能没有按预期工作：

1. 检查是否启用了数据捕获功能
2. 确认内容类型是否在支持列表中
3. 查看 agent 日志中是否有相关错误信息
4. 验证系统属性或环境变量是否正确设置

## 注意事项

- 脱敏后的数据使用 `****` 作为掩码
- 邮箱地址会保留域名部分以便于调试
- 脱敏是在数据发送到追踪后端之前进行的
- 如果脱敏过程中发生错误，原始数据不会被发送（fail-safe）
