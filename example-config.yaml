service_name: service_name
reporting:
  endpoint: http://localhost:4317

# Data capture configuration
data_capture:
  # Enable HTTP request and response body capture
  http_body:
    request: true
    response: true
  
  # Configure allowed content types for capture
  allowed_content_types:
    - application/json
    - application/xml
    - text/xml
    - application/x-www-form-urlencoded
    - text/plain

# Data sanitization is enabled by default
# To disable, use: -Dht.data.sanitization.enabled=false
# Or set environment variable: HT_DATA_SANITIZATION_ENABLED=false

# Disable specific filter providers (if needed)
# Use: -Dht.filter.provider.<provider-class-name>.disabled=true
