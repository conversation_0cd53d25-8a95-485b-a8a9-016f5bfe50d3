version: "1.0.0"
enabled: true
markersEnabled: false
markerFormat: "BRACKET"

rules:
  # Password fields rule
  - id: "password-fields"
    name: "Password Fields"
    description: "Sanitize common password field names"
    type: "FIELD_NAME"
    severity: "HIGH"
    enabled: true
    priority: 100
    fieldNames:
      - "password"
      - "passwd"
      - "pwd"
      - "secret"
      - "token"
      - "apiKey"
      - "api_key"
      - "accessToken"
      - "access_token"
      - "refreshToken"
      - "refresh_token"
    maskValue: "****"
    markerType: "PASSWORD"

  # Email pattern rule
  - id: "email-pattern"
    name: "Email Addresses"
    description: "Sanitize email addresses using pattern matching"
    type: "PATTERN"
    severity: "MEDIUM"
    enabled: true
    priority: 200
    pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
    contentTypes:
      - "application/json"
      - "application/xml"
      - "text/plain"
    maskValue: "****@****.***"
    markerType: "EMAIL"
    preserveFormat: true

  # Credit card rule
  - id: "credit-card"
    name: "Credit Card Numbers"
    description: "Sanitize credit card numbers"
    type: "PATTERN"
    severity: "CRITICAL"
    enabled: true
    priority: 50
    pattern: '\b(?:\d{4}[-\s]?){3}\d{4}\b'
    maskValue: "****-****-****-****"
    markerType: "CREDIT_CARD"

  # Phone number rule
  - id: "phone-numbers"
    name: "Phone Numbers"
    description: "Sanitize phone numbers"
    type: "PATTERN"
    severity: "MEDIUM"
    enabled: true
    priority: 150
    pattern: '\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}'
    maskValue: "***-***-****"
    markerType: "PHONE"

  # SSN rule
  - id: "ssn"
    name: "Social Security Numbers"
    description: "Sanitize US Social Security Numbers"
    type: "PATTERN"
    severity: "CRITICAL"
    enabled: true
    priority: 25
    pattern: '\b\d{3}-?\d{2}-?\d{4}\b'
    maskValue: "***-**-****"
    markerType: "SSN"

  # API Keys rule
  - id: "api-keys"
    name: "API Keys"
    description: "Sanitize API keys and tokens"
    type: "PATTERN"
    severity: "HIGH"
    enabled: true
    priority: 75
    pattern: '(?i)(api[_-]?key|token|secret)["\s]*[:=]["\s]*[a-zA-Z0-9_-]{16,}'
    maskValue: "****"
    markerType: "API_KEY"

  # Database connection strings
  - id: "db-connection"
    name: "Database Connection Strings"
    description: "Sanitize database connection strings"
    type: "PATTERN"
    severity: "HIGH"
    enabled: true
    priority: 80
    pattern: '(?i)(password|pwd)["\s]*[:=]["\s]*[^"\s;]+'
    maskValue: "****"
    markerType: "DB_PASSWORD"

  # Payment service specific rules
  - id: "payment-account-number"
    name: "Payment Account Numbers"
    description: "Sanitize account numbers for payment service"
    type: "FIELD_NAME"
    severity: "HIGH"
    enabled: true
    priority: 60
    fieldNames:
      - "accountNumber"
      - "account_number"
      - "routingNumber"
      - "routing_number"
      - "cvv"
      - "cvc"
      - "securityCode"
      - "security_code"
    maskValue: "***"
    markerType: "PAYMENT_DATA"
    includeServices:
      - "payment-service"
      - "billing-service"

  # User service specific rules
  - id: "user-pii"
    name: "User Personal Information"
    description: "Sanitize PII for user service"
    type: "FIELD_NAME"
    severity: "MEDIUM"
    enabled: true
    priority: 120
    fieldNames:
      - "firstName"
      - "first_name"
      - "lastName"
      - "last_name"
      - "fullName"
      - "full_name"
      - "dateOfBirth"
      - "date_of_birth"
      - "dob"
    maskValue: "***"
    markerType: "PII"
    includeServices:
      - "user-service"
      - "profile-service"

globalSettings:
  defaultMaskValue: "****"
  enableLogging: true
  logLevel: "INFO"
  maxRulesPriority: 1000
  enableMetrics: true
