package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds the application configuration
type Config struct {
	Server  ServerConfig  `yaml:"server"`
	Auth    AuthConfig    `yaml:"auth"`
	Rules   RulesConfig   `yaml:"rules"`
	Logging LoggingConfig `yaml:"logging"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port         int           `yaml:"port"`
	Host         string        `yaml:"host"`
	ReadTimeout  time.Duration `yaml:"readTimeout"`
	WriteTimeout time.Duration `yaml:"writeTimeout"`
	IdleTimeout  time.Duration `yaml:"idleTimeout"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	Enabled     bool   `yaml:"enabled"`
	JWTSecret   string `yaml:"jwtSecret"`
	TokenHeader string `yaml:"tokenHeader"`
}

// RulesConfig holds rules-related configuration
type RulesConfig struct {
	ConfigFile     string        `yaml:"configFile"`
	ReloadEnabled  bool          `yaml:"reloadEnabled"`
	ReloadInterval time.Duration `yaml:"reloadInterval"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// LoadConfig loads configuration from environment variables with defaults
func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         getEnvAsInt("SERVER_PORT", 8080),
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			ReadTimeout:  getEnvAsDuration("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getEnvAsDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
			IdleTimeout:  getEnvAsDuration("SERVER_IDLE_TIMEOUT", 120*time.Second),
		},
		Auth: AuthConfig{
			Enabled:     getEnvAsBool("AUTH_ENABLED", false),
			JWTSecret:   getEnv("JWT_SECRET", "your-secret-key"),
			TokenHeader: getEnv("TOKEN_HEADER", "Authorization"),
		},
		Rules: RulesConfig{
			ConfigFile:     getEnv("RULES_CONFIG_FILE", "config/rules.json"),
			ReloadEnabled:  getEnvAsBool("RULES_RELOAD_ENABLED", true),
			ReloadInterval: getEnvAsDuration("RULES_RELOAD_INTERVAL", 5*time.Minute),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
	}
}

// Helper functions to get environment variables with defaults
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
