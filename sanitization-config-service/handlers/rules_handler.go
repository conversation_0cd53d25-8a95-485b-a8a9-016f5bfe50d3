package handlers

import (
	"net/http"
	"sanitization-config-service/service"
	"time"

	"github.com/gin-gonic/gin"
)

// RulesHandler handles HTTP requests for sanitization rules
type RulesHandler struct {
	ruleService *service.RuleService
}

// NewRulesHandler creates a new rules handler
func NewRulesHandler(ruleService *service.RuleService) *RulesHandler {
	return &RulesHandler{
		ruleService: ruleService,
	}
}

// GetRules returns sanitization rules
// @Summary Get sanitization rules
// @Description Get sanitization rules, optionally filtered by service name
// @Tags rules
// @Accept json
// @Produce json
// @Param X-Service-Name header string false "Service name for filtering rules"
// @Success 200 {object} models.SanitizationConfig
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules [get]
func (h *RulesHandler) GetRules(c *gin.Context) {
	serviceName := c.<PERSON>eader("X-Service-Name")
	
	var config interface{}
	if serviceName != "" {
		config = h.ruleService.GetConfigForService(serviceName)
	} else {
		config = h.ruleService.GetConfig()
	}
	
	c.JSON(http.StatusOK, config)
}

// ReloadRules reloads rules from configuration file
// @Summary Reload sanitization rules
// @Description Reload sanitization rules from the configuration file
// @Tags rules
// @Accept json
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/reload [post]
func (h *RulesHandler) ReloadRules(c *gin.Context) {
	if err := h.ruleService.ReloadConfig(); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to reload configuration",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "Configuration reloaded successfully",
		Timestamp: time.Now().Unix(),
	})
}

// GetHealth returns service health status
// @Summary Health check
// @Description Get service health status
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /health [get]
func (h *RulesHandler) GetHealth(c *gin.Context) {
	config := h.ruleService.GetConfig()
	
	c.JSON(http.StatusOK, HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Unix(),
		Version:   config.Version,
		RuleCount: len(config.Rules),
	})
}

// GetMetrics returns service metrics
// @Summary Get service metrics
// @Description Get service metrics and statistics
// @Tags metrics
// @Accept json
// @Produce json
// @Success 200 {object} MetricsResponse
// @Router /metrics [get]
func (h *RulesHandler) GetMetrics(c *gin.Context) {
	config := h.ruleService.GetConfig()
	
	enabledRules := 0
	rulesByType := make(map[string]int)
	rulesBySeverity := make(map[string]int)
	
	for _, rule := range config.Rules {
		if rule.Enabled {
			enabledRules++
		}
		rulesByType[string(rule.Type)]++
		rulesBySeverity[string(rule.Severity)]++
	}
	
	c.JSON(http.StatusOK, MetricsResponse{
		TotalRules:      len(config.Rules),
		EnabledRules:    enabledRules,
		DisabledRules:   len(config.Rules) - enabledRules,
		RulesByType:     rulesByType,
		RulesBySeverity: rulesBySeverity,
		ConfigVersion:   config.Version,
		LastUpdated:     config.Timestamp,
	})
}

// Response types
type ErrorResponse struct {
	Error     string `json:"error"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type SuccessResponse struct {
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
	RuleCount int    `json:"ruleCount"`
}

type MetricsResponse struct {
	TotalRules      int            `json:"totalRules"`
	EnabledRules    int            `json:"enabledRules"`
	DisabledRules   int            `json:"disabledRules"`
	RulesByType     map[string]int `json:"rulesByType"`
	RulesBySeverity map[string]int `json:"rulesBySeverity"`
	ConfigVersion   string         `json:"configVersion"`
	LastUpdated     int64          `json:"lastUpdated"`
}
