# Sanitization Config Service Makefile

# Variables
APP_NAME := sanitization-config-service
VERSION := 1.0.0
DOCKER_IMAGE := $(APP_NAME):$(VERSION)
DOCKER_LATEST := $(APP_NAME):latest
GO_VERSION := 1.21
PORT := 8080

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

.PHONY: help build run test clean docker-build docker-run docker-stop deps fmt lint vet check install dev

# Default target
help: ## Show this help message
	@echo "$(BLUE)$(APP_NAME) - Makefile Help$(NC)"
	@echo "=================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
deps: ## Download dependencies
	@echo "$(YELLOW)Downloading dependencies...$(NC)"
	go mod download
	go mod tidy

fmt: ## Format Go code
	@echo "$(YELLOW)Formatting code...$(NC)"
	go fmt ./...

lint: ## Run golint
	@echo "$(YELLOW)Running linter...$(NC)"
	@if command -v golint >/dev/null 2>&1; then \
		golint ./...; \
	else \
		echo "$(RED)golint not installed. Install with: go install golang.org/x/lint/golint@latest$(NC)"; \
	fi

vet: ## Run go vet
	@echo "$(YELLOW)Running go vet...$(NC)"
	go vet ./...

check: fmt vet lint ## Run all code quality checks

# Build targets
build: deps ## Build the application
	@echo "$(YELLOW)Building $(APP_NAME)...$(NC)"
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags "-X main.version=$(VERSION)" -o $(APP_NAME) .
	@echo "$(GREEN)Build completed: $(APP_NAME)$(NC)"

build-local: deps ## Build for local development
	@echo "$(YELLOW)Building $(APP_NAME) for local development...$(NC)"
	go build -ldflags "-X main.version=$(VERSION)" -o $(APP_NAME) .
	@echo "$(GREEN)Local build completed: $(APP_NAME)$(NC)"

install: build-local ## Install the application
	@echo "$(YELLOW)Installing $(APP_NAME)...$(NC)"
	sudo mv $(APP_NAME) /usr/local/bin/
	@echo "$(GREEN)Installation completed$(NC)"

# Run targets
run: build-local ## Run the application locally
	@echo "$(YELLOW)Starting $(APP_NAME)...$(NC)"
	./$(APP_NAME)

dev: ## Run in development mode with auto-reload
	@echo "$(YELLOW)Starting $(APP_NAME) in development mode...$(NC)"
	@if command -v air >/dev/null 2>&1; then \
		air; \
	else \
		echo "$(RED)air not installed. Install with: go install github.com/cosmtrek/air@latest$(NC)"; \
		echo "$(YELLOW)Falling back to normal run...$(NC)"; \
		$(MAKE) run; \
	fi

# Test targets
test: ## Run tests
	@echo "$(YELLOW)Running tests...$(NC)"
	go test -v ./...

test-coverage: ## Run tests with coverage
	@echo "$(YELLOW)Running tests with coverage...$(NC)"
	go test -v -cover ./...
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)Coverage report generated: coverage.html$(NC)"

test-service: ## Test the running service
	@echo "$(YELLOW)Testing service endpoints...$(NC)"
	@chmod +x test_service.sh
	./test_service.sh

# Docker targets
docker-build: ## Build Docker image
	@echo "$(YELLOW)Building Docker image...$(NC)"
	docker build -t $(DOCKER_IMAGE) -t $(DOCKER_LATEST) .
	@echo "$(GREEN)Docker image built: $(DOCKER_IMAGE)$(NC)"

docker-run: docker-build ## Run Docker container
	@echo "$(YELLOW)Starting Docker container...$(NC)"
	docker run -d --name $(APP_NAME) -p $(PORT):$(PORT) \
		-v $(PWD)/config:/app/config:ro \
		$(DOCKER_LATEST)
	@echo "$(GREEN)Container started on port $(PORT)$(NC)"

docker-stop: ## Stop Docker container
	@echo "$(YELLOW)Stopping Docker container...$(NC)"
	-docker stop $(APP_NAME)
	-docker rm $(APP_NAME)
	@echo "$(GREEN)Container stopped$(NC)"

docker-logs: ## Show Docker container logs
	docker logs -f $(APP_NAME)

# Docker Compose targets
compose-up: ## Start services with Docker Compose
	@echo "$(YELLOW)Starting services with Docker Compose...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Services started$(NC)"

compose-down: ## Stop services with Docker Compose
	@echo "$(YELLOW)Stopping services with Docker Compose...$(NC)"
	docker-compose down
	@echo "$(GREEN)Services stopped$(NC)"

compose-logs: ## Show Docker Compose logs
	docker-compose logs -f

compose-build: ## Build services with Docker Compose
	@echo "$(YELLOW)Building services with Docker Compose...$(NC)"
	docker-compose build
	@echo "$(GREEN)Services built$(NC)"

# Utility targets
clean: ## Clean build artifacts
	@echo "$(YELLOW)Cleaning build artifacts...$(NC)"
	rm -f $(APP_NAME)
	rm -f coverage.out coverage.html
	go clean
	@echo "$(GREEN)Clean completed$(NC)"

clean-docker: ## Clean Docker images and containers
	@echo "$(YELLOW)Cleaning Docker artifacts...$(NC)"
	-docker stop $(APP_NAME)
	-docker rm $(APP_NAME)
	-docker rmi $(DOCKER_IMAGE) $(DOCKER_LATEST)
	@echo "$(GREEN)Docker clean completed$(NC)"

status: ## Show service status
	@echo "$(BLUE)Service Status$(NC)"
	@echo "=============="
	@if curl -s http://localhost:$(PORT)/health >/dev/null 2>&1; then \
		echo "$(GREEN)✅ Service is running on port $(PORT)$(NC)"; \
		curl -s http://localhost:$(PORT)/health | python3 -m json.tool 2>/dev/null || echo "Health check response received"; \
	else \
		echo "$(RED)❌ Service is not running$(NC)"; \
	fi

# Production targets
deploy-prod: docker-build ## Deploy to production (customize as needed)
	@echo "$(YELLOW)Deploying to production...$(NC)"
	@echo "$(RED)⚠️  Customize this target for your production environment$(NC)"
	# Add your production deployment commands here
	# Example: kubectl apply -f k8s/
	# Example: docker push $(DOCKER_IMAGE)

# Quick start
quick-start: deps build run ## Quick start: deps + build + run

# All-in-one development setup
setup: deps check build test ## Setup development environment

# Show configuration
config: ## Show current configuration
	@echo "$(BLUE)Current Configuration$(NC)"
	@echo "====================="
	@echo "App Name: $(APP_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Docker Image: $(DOCKER_IMAGE)"
	@echo "Port: $(PORT)"
	@echo "Go Version: $(GO_VERSION)"
