#!/bin/bash

# Sanitization Config Service Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEFAULT_PORT=8081
PORT=${SERVER_PORT:-$DEFAULT_PORT}
CONFIG_FILE=${RULES_CONFIG_FILE:-"config/rules.json"}

echo -e "${BLUE}🚀 Starting Sanitization Config Service${NC}"
echo "========================================"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go is not installed. Please install Go 1.21 or later.${NC}"
    exit 1
fi

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${YELLOW}⚠️  Config file not found: $CONFIG_FILE${NC}"
    echo -e "${YELLOW}   Using default rules...${NC}"
fi

# Build the application if binary doesn't exist or source is newer
if [ ! -f "sanitization-config-service" ] || [ "main.go" -nt "sanitization-config-service" ]; then
    echo -e "${YELLOW}🔨 Building application...${NC}"
    go build -o sanitization-config-service .
    echo -e "${GREEN}✅ Build completed${NC}"
fi

# Check if port is available
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}❌ Port $PORT is already in use${NC}"
    echo -e "${YELLOW}   Try using a different port: SERVER_PORT=8082 $0${NC}"
    exit 1
fi

# Set environment variables
export SERVER_PORT=$PORT
export RULES_CONFIG_FILE=$CONFIG_FILE
export LOG_LEVEL=${LOG_LEVEL:-"info"}
export AUTH_ENABLED=${AUTH_ENABLED:-"false"}

echo -e "${GREEN}📋 Configuration:${NC}"
echo "   Port: $PORT"
echo "   Config File: $CONFIG_FILE"
echo "   Log Level: $LOG_LEVEL"
echo "   Auth Enabled: $AUTH_ENABLED"
echo ""

# Start the service
echo -e "${GREEN}🎯 Starting service on port $PORT...${NC}"
echo -e "${BLUE}   Health check: http://localhost:$PORT/health${NC}"
echo -e "${BLUE}   API endpoint: http://localhost:$PORT/api/sanitization/rules${NC}"
echo -e "${BLUE}   Metrics: http://localhost:$PORT/metrics${NC}"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop the service${NC}"
echo ""

# Handle graceful shutdown
trap 'echo -e "\n${YELLOW}🛑 Shutting down service...${NC}"; exit 0' INT TERM

# Start the service
./sanitization-config-service
