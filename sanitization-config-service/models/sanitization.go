package models

import "time"

// RuleType represents the type of sanitization rule
type RuleType string

const (
	FieldNameRule  RuleType = "FIELD_NAME"
	PatternRule    RuleType = "PATTERN"
	ContentTypeRule RuleType = "CONTENT_TYPE"
	CustomRule     RuleType = "CUSTOM"
)

// SeverityLevel represents the severity level of a rule
type SeverityLevel string

const (
	LowSeverity      SeverityLevel = "LOW"
	MediumSeverity   SeverityLevel = "MEDIUM"
	HighSeverity     SeverityLevel = "HIGH"
	CriticalSeverity SeverityLevel = "CRITICAL"
)

// SanitizationRule represents a single sanitization rule
type SanitizationRule struct {
	ID          string            `json:"id" yaml:"id"`
	Name        string            `json:"name" yaml:"name"`
	Description string            `json:"description" yaml:"description"`
	Type        RuleType          `json:"type" yaml:"type"`
	Severity    SeverityLevel     `json:"severity" yaml:"severity"`
	Enabled     bool              `json:"enabled" yaml:"enabled"`
	Priority    int               `json:"priority" yaml:"priority"`
	
	// Rule matching conditions
	FieldNames   []string `json:"fieldNames,omitempty" yaml:"fieldNames,omitempty"`
	Pattern      string   `json:"pattern,omitempty" yaml:"pattern,omitempty"`
	ContentTypes []string `json:"contentTypes,omitempty" yaml:"contentTypes,omitempty"`
	
	// Sanitization configuration
	MaskValue       string `json:"maskValue" yaml:"maskValue"`
	MarkerType      string `json:"markerType,omitempty" yaml:"markerType,omitempty"`
	PreserveFormat  bool   `json:"preserveFormat" yaml:"preserveFormat"`
	PreserveLength  int    `json:"preserveLength" yaml:"preserveLength"`
	
	// Application conditions
	IncludeServices []string          `json:"includeServices,omitempty" yaml:"includeServices,omitempty"`
	ExcludeServices []string          `json:"excludeServices,omitempty" yaml:"excludeServices,omitempty"`
	Conditions      map[string]string `json:"conditions,omitempty" yaml:"conditions,omitempty"`
}

// SanitizationConfig represents the complete sanitization configuration
type SanitizationConfig struct {
	Version        string                 `json:"version" yaml:"version"`
	Timestamp      int64                  `json:"timestamp" yaml:"timestamp"`
	Enabled        bool                   `json:"enabled" yaml:"enabled"`
	MarkersEnabled bool                   `json:"markersEnabled" yaml:"markersEnabled"`
	MarkerFormat   string                 `json:"markerFormat" yaml:"markerFormat"`
	Rules          []SanitizationRule     `json:"rules" yaml:"rules"`
	GlobalSettings map[string]interface{} `json:"globalSettings,omitempty" yaml:"globalSettings,omitempty"`
}

// NewSanitizationConfig creates a new sanitization configuration with default values
func NewSanitizationConfig() *SanitizationConfig {
	return &SanitizationConfig{
		Version:        "1.0.0",
		Timestamp:      time.Now().UnixMilli(),
		Enabled:        true,
		MarkersEnabled: false,
		MarkerFormat:   "BRACKET",
		Rules:          make([]SanitizationRule, 0),
		GlobalSettings: make(map[string]interface{}),
	}
}

// AddRule adds a new rule to the configuration
func (c *SanitizationConfig) AddRule(rule SanitizationRule) {
	c.Rules = append(c.Rules, rule)
}

// GetRulesForService returns rules applicable to a specific service
func (c *SanitizationConfig) GetRulesForService(serviceName string) []SanitizationRule {
	var applicableRules []SanitizationRule
	
	for _, rule := range c.Rules {
		if !rule.Enabled {
			continue
		}
		
		// Check if service is excluded
		if contains(rule.ExcludeServices, serviceName) {
			continue
		}
		
		// Check if service is included (if include list is specified)
		if len(rule.IncludeServices) > 0 && !contains(rule.IncludeServices, serviceName) {
			continue
		}
		
		applicableRules = append(applicableRules, rule)
	}
	
	return applicableRules
}

// contains checks if a slice contains a specific string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// Validate validates the sanitization configuration
func (c *SanitizationConfig) Validate() error {
	if c.Version == "" {
		c.Version = "1.0.0"
	}
	
	if c.Timestamp == 0 {
		c.Timestamp = time.Now().UnixMilli()
	}
	
	// Validate rules
	for i, rule := range c.Rules {
		if rule.ID == "" {
			return &ValidationError{Field: "rules[" + string(rune(i)) + "].id", Message: "Rule ID is required"}
		}
		
		if rule.Type == "" {
			return &ValidationError{Field: "rules[" + string(rune(i)) + "].type", Message: "Rule type is required"}
		}
		
		if rule.MaskValue == "" {
			c.Rules[i].MaskValue = "****"
		}
	}
	
	return nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}
