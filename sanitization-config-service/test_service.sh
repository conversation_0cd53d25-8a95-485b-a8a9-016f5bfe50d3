#!/bin/bash

# Test script for Sanitization Config Service
set -e

BASE_URL="http://localhost:8081"
SERVICE_NAME="payment-service"

echo "🧪 Testing Sanitization Config Service"
echo "======================================="

# Function to make HTTP requests and pretty print JSON
make_request() {
    local method=$1
    local url=$2
    local headers=$3
    
    echo "📡 $method $url"
    if [ -n "$headers" ]; then
        echo "   Headers: $headers"
    fi
    
    if [ "$method" = "GET" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -H "$headers" "$url")
        else
            response=$(curl -s "$url")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -X "$method" -H "$headers" "$url")
        else
            response=$(curl -s -X "$method" "$url")
        fi
    fi
    
    echo "   Response:"
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    echo ""
}

# Check if service is running
echo "🔍 Checking if service is running..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ Service is not running. Please start it first:"
    echo "   docker-compose up -d"
    echo "   or"
    echo "   go run main.go"
    exit 1
fi
echo "✅ Service is running"
echo ""

# Test 1: Health check
echo "Test 1: Health Check"
echo "--------------------"
make_request "GET" "$BASE_URL/health"

# Test 2: Get all rules
echo "Test 2: Get All Rules"
echo "--------------------"
make_request "GET" "$BASE_URL/api/sanitization/rules"

# Test 3: Get rules for specific service
echo "Test 3: Get Rules for Specific Service"
echo "--------------------------------------"
make_request "GET" "$BASE_URL/api/sanitization/rules" "X-Service-Name: $SERVICE_NAME"

# Test 4: Get metrics
echo "Test 4: Get Metrics"
echo "------------------"
make_request "GET" "$BASE_URL/metrics"

# Test 5: Reload configuration
echo "Test 5: Reload Configuration"
echo "----------------------------"
make_request "POST" "$BASE_URL/api/sanitization/rules/reload"

# Test 6: Root endpoint
echo "Test 6: Root Endpoint"
echo "--------------------"
make_request "GET" "$BASE_URL/"

# Test 7: Test with different service names
echo "Test 7: Test Different Service Names"
echo "------------------------------------"
services=("user-service" "billing-service" "unknown-service")

for service in "${services[@]}"; do
    echo "   Testing with service: $service"
    make_request "GET" "$BASE_URL/api/sanitization/rules" "X-Service-Name: $service"
done

# Test 8: Performance test (optional)
echo "Test 8: Basic Performance Test"
echo "------------------------------"
echo "Making 10 concurrent requests..."

start_time=$(date +%s.%N)
for i in {1..10}; do
    curl -s "$BASE_URL/api/sanitization/rules" > /dev/null &
done
wait
end_time=$(date +%s.%N)

duration=$(echo "$end_time - $start_time" | bc)
echo "   Completed 10 requests in ${duration}s"
echo ""

# Test 9: Error handling
echo "Test 9: Error Handling"
echo "----------------------"
echo "   Testing non-existent endpoint:"
curl -s "$BASE_URL/api/nonexistent" || echo "   Expected 404 error"
echo ""

echo "🎉 All tests completed!"
echo ""
echo "📊 Summary:"
echo "- Health check: ✅"
echo "- Get all rules: ✅"
echo "- Service-specific rules: ✅"
echo "- Metrics: ✅"
echo "- Config reload: ✅"
echo "- Performance: ✅"
echo "- Error handling: ✅"
echo ""
echo "🔗 Useful endpoints:"
echo "- Health: $BASE_URL/health"
echo "- Rules: $BASE_URL/api/sanitization/rules"
echo "- Metrics: $BASE_URL/metrics"
echo "- Reload: $BASE_URL/api/sanitization/rules/reload"
