# Integration Test Guide

This guide demonstrates how to test the integration between the Hypertrace Java Agent and the Go-based Sanitization Config Service.

## Prerequisites

1. **Go Service Running**: The sanitization config service should be running on port 8081
2. **Java Agent Built**: The Hypertrace Java Agent should be built with the dynamic sanitization features
3. **Test Application**: A simple Java application to test with

## Step 1: Start the Go Service

```bash
cd sanitization-config-service
./start.sh
```

The service should start and show:
```
🚀 Starting Sanitization Config Service
========================================
✅ Build completed
📋 Configuration:
   Port: 8081
   Config File: config/rules.json
   Log Level: info
   Auth Enabled: false

🎯 Starting service on port 8081...
   Health check: http://localhost:8081/health
   API endpoint: http://localhost:8081/api/sanitization/rules
   Metrics: http://localhost:8081/metrics
```

## Step 2: Verify Service is Working

```bash
# Test health endpoint
curl http://localhost:8081/health

# Test rules endpoint
curl http://localhost:8081/api/sanitization/rules

# Test service-specific rules
curl -H "X-Service-Name: payment-service" http://localhost:8081/api/sanitization/rules
```

## Step 3: Configure Java Agent

Set the following environment variables for your Java application:

```bash
export HT_SANITIZATION_CONFIG_ENDPOINT="http://localhost:8081/api/sanitization/rules"
export HT_SANITIZATION_CONFIG_REFRESH_INTERVAL="300"
export HT_SERVICE_NAME="test-service"
export HT_REPORTING_ENDPOINT="http://localhost:4317"
```

## Step 4: Create Test Application

Create a simple Spring Boot application to test:

```java
@RestController
public class TestController {
    
    @PostMapping("/api/user")
    public ResponseEntity<String> createUser(@RequestBody Map<String, Object> userData) {
        // This will be traced and sanitized by the agent
        System.out.println("Received user data: " + userData);
        return ResponseEntity.ok("User created");
    }
    
    @PostMapping("/api/payment")
    public ResponseEntity<String> processPayment(@RequestBody Map<String, Object> paymentData) {
        // This will be traced and sanitized by the agent
        System.out.println("Received payment data: " + paymentData);
        return ResponseEntity.ok("Payment processed");
    }
}
```

## Step 5: Run Test Application with Agent

```bash
java -javaagent:path/to/hypertrace-agent.jar \
     -Dht.sanitization.config.endpoint=http://localhost:8081/api/sanitization/rules \
     -Dht.sanitization.config.refresh.interval=300 \
     -Dht.service.name=test-service \
     -jar your-test-app.jar
```

## Step 6: Test Data Sanitization

Send test requests with sensitive data:

```bash
# Test user data with email and password
curl -X POST http://localhost:8080/api/user \
     -H "Content-Type: application/json" \
     -d '{
       "username": "john.doe",
       "email": "<EMAIL>",
       "password": "secretpassword123",
       "phone": "******-123-4567"
     }'

# Test payment data
curl -X POST http://localhost:8080/api/payment \
     -H "Content-Type: application/json" \
     -d '{
       "accountNumber": "****************",
       "routingNumber": "*********",
       "cvv": "123",
       "amount": 100.00
     }'
```

## Step 7: Verify Sanitization

Check the application logs and traces to verify that sensitive data is properly sanitized:

### Expected Sanitized Output

**User Data:**
```json
{
  "username": "john.doe",
  "email": "****@****.***",
  "password": "****",
  "phone": "***-***-****"
}
```

**Payment Data:**
```json
{
  "accountNumber": "***",
  "routingNumber": "***",
  "cvv": "***",
  "amount": 100.00
}
```

## Step 8: Test Dynamic Rule Updates

1. **Modify Rules**: Edit `config/rules.json` to add/modify rules
2. **Reload Config**: Send POST request to reload endpoint
   ```bash
   curl -X POST http://localhost:8081/api/sanitization/rules/reload
   ```
3. **Wait for Agent Refresh**: Wait for the configured refresh interval (5 minutes by default)
4. **Test New Rules**: Send requests to verify new rules are applied

## Step 9: Monitor Service

Check service metrics and health:

```bash
# Service health
curl http://localhost:8081/health

# Service metrics
curl http://localhost:8081/metrics

# Check logs for rule loading
# Look for messages like:
# "Successfully updated sanitization rules: version=1.0.0, rules=9"
```

## Troubleshooting

### Common Issues

1. **Service Not Accessible**
   - Check if service is running: `curl http://localhost:8081/health`
   - Verify port is not blocked by firewall
   - Check service logs for errors

2. **Rules Not Loading**
   - Verify endpoint URL is correct
   - Check network connectivity between Java app and Go service
   - Look for error messages in Java agent logs

3. **Rules Not Applied**
   - Verify service name matches between agent and rules
   - Check rule priorities and conditions
   - Ensure rules are enabled in configuration

4. **Performance Issues**
   - Monitor service response times
   - Check rule complexity (especially regex patterns)
   - Adjust refresh interval if needed

### Debug Mode

Enable debug logging in both services:

**Go Service:**
```bash
LOG_LEVEL=debug ./start.sh
```

**Java Agent:**
```bash
-Dht.log.level=DEBUG
```

## Expected Log Messages

### Go Service Logs
```
INFO  - Loaded configuration with 9 rules
INFO  - Started config reloader with interval: 5m0s
INFO  - Starting server on 0.0.0.0:8081
```

### Java Agent Logs
```
INFO  - Dynamic sanitization rule manager configured: endpoint=http://localhost:8081/api/sanitization/rules, refreshInterval=PT5M
INFO  - Started dynamic sanitization rule manager with refresh interval: PT5M
INFO  - Successfully updated sanitization rules: version=1.0.0, rules=9
```

## Performance Benchmarks

Expected performance characteristics:

- **Rule Fetch Time**: < 100ms
- **Rule Application**: < 1ms per request
- **Memory Usage**: < 50MB for Go service
- **CPU Usage**: < 5% under normal load

## Conclusion

If all steps complete successfully, you have verified that:

1. ✅ Go service provides sanitization rules via REST API
2. ✅ Java agent fetches rules from Go service at startup
3. ✅ Java agent applies rules to sanitize sensitive data
4. ✅ Rules can be updated dynamically without restarting applications
5. ✅ Service-specific rules are properly filtered
6. ✅ Performance is acceptable for production use
