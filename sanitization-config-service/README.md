# Sanitization Config Service

A Go-based HTTP service that provides dynamic sanitization rules for the Hypertrace Java Agent. This service allows centralized management of sensitive data detection and sanitization rules without requiring application restarts.

## Features

- **RESTful API** for retrieving sanitization rules
- **Service-specific filtering** based on service names
- **Hot reloading** of configuration files
- **JWT authentication** support (optional)
- **Health checks** and metrics endpoints
- **Rate limiting** and CORS support
- **Docker support** for easy deployment

## Quick Start

### Using Docker Compose

```bash
# Clone or create the service directory
cd sanitization-config-service

# Start the service
docker-compose up -d

# Check service health
curl http://localhost:8080/health
```

### Manual Build and Run

```bash
# Install dependencies
go mod tidy

# Run the service
go run main.go

# Or build and run
go build -o sanitization-config-service
./sanitization-config-service
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_PORT` | `8080` | HTTP server port |
| `SERVER_HOST` | `0.0.0.0` | HTTP server host |
| `AUTH_ENABLED` | `false` | Enable JWT authentication |
| `JWT_SECRET` | `your-secret-key` | JWT signing secret |
| `RULES_CONFIG_FILE` | `config/rules.yaml` | Path to rules configuration file |
| `RULES_RELOAD_ENABLED` | `true` | Enable automatic config reloading |
| `RULES_RELOAD_INTERVAL` | `5m` | Config reload interval |
| `LOG_LEVEL` | `info` | Logging level (debug, info, warn, error) |
| `LOG_FORMAT` | `json` | Log format (json, text) |

### Rules Configuration

Rules are defined in YAML format. See `config/rules.yaml` for a complete example.

```yaml
version: "1.0.0"
enabled: true
markersEnabled: false
markerFormat: "BRACKET"

rules:
  - id: "password-fields"
    name: "Password Fields"
    type: "FIELD_NAME"
    severity: "HIGH"
    enabled: true
    priority: 100
    fieldNames: ["password", "passwd", "pwd"]
    maskValue: "****"
    markerType: "PASSWORD"
```

## API Endpoints

### Get Sanitization Rules

```bash
# Get all rules
curl http://localhost:8080/api/sanitization/rules

# Get rules for specific service
curl -H "X-Service-Name: payment-service" \
     http://localhost:8080/api/sanitization/rules
```

### Reload Configuration

```bash
curl -X POST http://localhost:8080/api/sanitization/rules/reload
```

### Health Check

```bash
curl http://localhost:8080/health
```

### Metrics

```bash
curl http://localhost:8080/metrics
```

## Rule Types

### Field Name Rules
Match based on JSON/XML field names:

```yaml
- id: "password-rule"
  type: "FIELD_NAME"
  fieldNames: ["password", "secret"]
  maskValue: "****"
```

### Pattern Rules
Match using regular expressions:

```yaml
- id: "email-rule"
  type: "PATTERN"
  pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
  maskValue: "****@****.***"
```

### Content Type Rules
Apply rules based on HTTP content types:

```yaml
- id: "json-rule"
  type: "CONTENT_TYPE"
  contentTypes: ["application/json"]
```

## Service-Specific Rules

Rules can be applied to specific services:

```yaml
- id: "payment-rule"
  includeServices: ["payment-service"]
  excludeServices: ["public-api"]
```

## Authentication

Enable JWT authentication by setting `AUTH_ENABLED=true`:

```bash
# Generate a token (example)
curl -X POST http://localhost:8080/auth/token \
     -H "Content-Type: application/json" \
     -d '{"service_name": "my-service"}'

# Use token in requests
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/sanitization/rules
```

## Deployment

### Docker

```bash
# Build image
docker build -t sanitization-config-service .

# Run container
docker run -p 8080:8080 \
           -v $(pwd)/config:/app/config:ro \
           sanitization-config-service
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sanitization-config-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sanitization-config-service
  template:
    metadata:
      labels:
        app: sanitization-config-service
    spec:
      containers:
      - name: service
        image: sanitization-config-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: RULES_CONFIG_FILE
          value: "/config/rules.yaml"
        volumeMounts:
        - name: config
          mountPath: /config
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: sanitization-rules
```

## Monitoring

### Health Checks

The service provides health check endpoints:

- `GET /health` - Basic health status
- `GET /metrics` - Detailed metrics

### Logging

Structured logging with configurable levels and formats:

```json
{
  "level": "info",
  "timestamp": "2023-12-01T10:00:00Z",
  "message": "Config reloaded successfully",
  "rules_count": 10
}
```

## Development

### Project Structure

```
sanitization-config-service/
├── main.go                 # Application entry point
├── config/                 # Configuration package
│   ├── config.go          # Configuration loading
│   └── rules.yaml         # Default rules
├── models/                 # Data models
│   └── sanitization.go    # Rule models
├── service/               # Business logic
│   └── rule_service.go    # Rule management
├── handlers/              # HTTP handlers
│   └── rules_handler.go   # API endpoints
├── middleware/            # HTTP middleware
│   └── auth.go           # Authentication & CORS
├── Dockerfile            # Docker build
├── docker-compose.yml    # Docker Compose
└── README.md            # This file
```

### Adding New Rule Types

1. Add new rule type to `models/sanitization.go`
2. Implement validation logic in `Validate()` method
3. Update rule processing in Java agent

### Testing

```bash
# Run tests
go test ./...

# Run with coverage
go test -cover ./...

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## Security Considerations

- Use HTTPS in production
- Enable JWT authentication for sensitive environments
- Regularly rotate JWT secrets
- Implement proper rate limiting
- Monitor for suspicious access patterns
- Keep dependencies updated

## Troubleshooting

### Common Issues

1. **Config file not found**: Check `RULES_CONFIG_FILE` path
2. **Permission denied**: Ensure proper file permissions
3. **Invalid YAML**: Validate YAML syntax
4. **Port conflicts**: Change `SERVER_PORT` if needed

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug ./sanitization-config-service
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and add tests
4. Submit a pull request

## License

This project is licensed under the Apache License 2.0.
