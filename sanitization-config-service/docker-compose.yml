version: '3.8'

services:
  sanitization-config-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      - AUTH_ENABLED=false
      - JWT_SECRET=your-secret-key-change-in-production
      - RULES_CONFIG_FILE=/app/config/rules.yaml
      - RULES_RELOAD_ENABLED=true
      - RULES_RELOAD_INTERVAL=5m
      - LOG_LEVEL=info
      - LOG_FORMAT=json
    volumes:
      - ./config/rules.yaml:/app/config/rules.yaml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - sanitization-network

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - sanitization-config-service
    networks:
      - sanitization-network
    profiles:
      - production

networks:
  sanitization-network:
    driver: bridge
