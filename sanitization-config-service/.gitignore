# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
sanitization-config-service

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# Configuration files with secrets (keep templates)
config/secrets.yaml
config/production.yaml
config/local.yaml

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Build artifacts
dist/
build/

# Air (live reload) temporary files
tmp/
