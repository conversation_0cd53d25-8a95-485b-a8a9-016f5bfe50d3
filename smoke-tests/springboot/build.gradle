plugins {
    id 'org.springframework.boot' version '2.3.2.RELEASE'
    id 'io.spring.dependency-management' version '1.0.9.RELEASE'
    id 'java'
    id 'com.google.cloud.tools.jib' version '2.5.0'
}

version '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    implementation platform("io.opentelemetry:opentelemetry-bom:1.0.0")

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'io.opentelemetry:opentelemetry-extension-annotations'
    implementation 'io.opentelemetry:opentelemetry-api'
}

compileJava {
    options.release.set(8)
}

def targetJDK = project.hasProperty("targetJDK") ? project.targetJDK : 11

def tag = findProperty("tag") ?: new Date().format("yyyyMMdd.HHmmSS")

jib {
    from.image = "bellsoft/liberica-openjdk-alpine:$targetJDK"
    to.image = "hypertrace/java-agent-test-containers:smoke-springboot-jdk$targetJDK-$tag"
    container.ports = ["8080"]
}
