<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
  version="3.1">
  <listener>
    <listener-class>io.opentelemetry.smoketest.matrix.ExceptionRequestListener</listener-class>
  </listener>
  <servlet>
    <servlet-name>Headers</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.HeaderDumpingServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Greeting</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.GreetingServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>AsyncGreeting</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.AsyncGreetingServlet</servlet-class>
    <async-supported>true</async-supported>
  </servlet>
  <servlet>
    <servlet-name>Exception</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.ExceptionServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Forward</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.ForwardServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Include</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.IncludeServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Jsp</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.JspServlet</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Echo</servlet-name>
    <servlet-class>io.opentelemetry.smoketest.matrix.EchoServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>Headers</servlet-name>
    <url-pattern>/headers</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Greeting</servlet-name>
    <url-pattern>/greeting</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>AsyncGreeting</servlet-name>
    <url-pattern>/asyncgreeting</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Exception</servlet-name>
    <url-pattern>/exception</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Forward</servlet-name>
    <url-pattern>/forward</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Include</servlet-name>
    <url-pattern>/include</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Jsp</servlet-name>
    <url-pattern>/jsp</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Echo</servlet-name>
    <url-pattern>/echo</url-pattern>
  </servlet-mapping>
</web-app>