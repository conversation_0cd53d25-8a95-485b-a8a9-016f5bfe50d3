import com.bmuschko.gradle.docker.tasks.image.DockerBuildImage
import com.bmuschko.gradle.docker.tasks.image.DockerPushImage

plugins {
  id "war"
  id "com.bmuschko.docker-remote-api" version "6.6.1"
}

compileJava {
  options.release.set(8)
}

repositories {
  mavenCentral()
}
dependencies {
  implementation("javax.servlet:javax.servlet-api:3.0.1")
}

def buildMatrixTask = tasks.create("buildMatrix") {
  group = "build"
  description = "Builds all Docker images for the test matrix"
}

Set<String> matrix = []
tasks.create("pushMatrix", DockerPushImage) {
  group = "publishing"
  description = "Push all Docker images for the test matrix"
  dependsOn(buildMatrixTask)
  images.set(matrix)
}

// Each line under appserver describes one matrix of (version x vm x jdk), dockerfile key overrides
// Dockerfile name, args key passes raw arguments to docker build
def targets = [
        "jetty": [
                [version: ["9.4.35"], vm: ["hotspot", "openj9"], jdk: ["8", "11", "15"]],
                [version: ["10.0.0"], vm: ["hotspot", "openj9"], jdk: ["11", "15"]],
        ],
        "tomcat": [
                [version: ["7.0.107"], vm: ["hotspot", "openj9"], jdk: ["8"]],
                [version: ["8.5.60", "9.0.40", "10.0.0"], vm: ["hotspot", "openj9"], jdk: ["8", "11"]]
        ],
        "tomee": [
                [version: ["7.0.0"], vm: ["hotspot"], jdk: ["8"]],
                [version: ["8.0.6"], vm: ["hotspot"], jdk: ["8", "11"]],
                [version: ["7.0.0"], vm: ["openj9"], jdk: ["8"], dockerfile: "tomee-custom"],
                [version: ["8.0.6"], vm: ["openj9"], jdk: ["8", "11"], dockerfile: "tomee-custom"]
        ],
        "payara": [
                [version: ["5.2020.6"], vm: ["hotspot"], jdk: ["8"], args: [tagSuffix: ""]],
                [version: ["5.2020.6"], vm: ["hotspot"], jdk: ["11"], args: [tagSuffix: "-jdk11"]],
                [version: ["5.2020.6"], vm: ["openj9"], jdk: ["8", "11"], dockerfile: "payara-custom-5.2020.6"]
        ],
        "wildfly": [
                [version: ["13.0.0.Final"], vm: ["hotspot", "openj9"], jdk: ["8"]],
                [version: ["17.0.1.Final", "21.0.0.Final"], vm: ["hotspot", "openj9"], jdk: ["8", "11", "15"]]
        ],
        "liberty": [
                [version: ["20.0.0.12"], vm: ["hotspot", "openj9"], jdk: ["8", "11", "15"]]
        ]
]

def configureImage(server, dockerfile, version, vm, jdk, Map<String, String> extraArgs, extraTag) {
  def dockerWorkingDir = new File(project.buildDir, "docker")

  def prepareTask = tasks.register("${server}ImagePrepare-$version-jdk$jdk-$vm", Copy) {
    def warTask = project.tasks.war
    it.dependsOn(warTask)
    it.into(dockerWorkingDir)
    it.from("src")
    it.from(warTask.archiveFile) {
      rename { _ -> "app.war" }
    }
  }
  def vmSuffix = vm == "hotspot" ? "" : "-$vm"
  def image = "hypertrace/java-agent-test-containers:$server-$version-jdk$jdk$vmSuffix-$extraTag"

  def buildTask = tasks.register("${server}Image-$version-jdk$jdk$vmSuffix", DockerBuildImage) {
    it.dependsOn(prepareTask)
    group = "build"
    description = "Builds Docker image with $server $version on JDK $jdk"

    it.inputDir.set(dockerWorkingDir)
    it.images.add(image)
    it.dockerFile.set(new File(dockerWorkingDir, dockerfile))
    it.buildArgs.set(extraArgs + [jdk: jdk, vm: vm, version: version])
  }

  project.tasks.buildMatrix.dependsOn(buildTask)
  return image
}

def extraTag = findProperty("extraTag") ?: new Date().format("yyyyMMdd.HHmmSS")
targets.each { server, matrices ->
  matrices.forEach { entry ->
    def dockerfile = (entry["dockerfile"]?.toString() ?: server) + ".dockerfile"
    def extraArgs = (entry["args"] ?: [:]) as Map<String, String>

    entry.version.forEach { version ->
      entry.vm.forEach { vm ->
        entry.jdk.forEach { jdk ->
          matrix.add(configureImage(server, dockerfile, version, vm, jdk, extraArgs, extraTag))
        }
      }
    }
  }
}

assemble.dependsOn(buildMatrixTask)