#!/bin/bash

# End-to-End Integration Test for Dynamic Sanitization
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 End-to-End Integration Test for Dynamic Sanitization${NC}"
echo "=========================================================="

# Configuration
GO_SERVICE_PORT=8081
GO_SERVICE_URL="http://localhost:$GO_SERVICE_PORT"

echo -e "${YELLOW}📋 Test Configuration:${NC}"
echo "   Go Service URL: $GO_SERVICE_URL"
echo "   Go Service Port: $GO_SERVICE_PORT"
echo ""

# Step 1: Check if Go service is running
echo -e "${BLUE}Step 1: Checking Go Service${NC}"
echo "----------------------------"

if curl -s "$GO_SERVICE_URL/health" > /dev/null; then
    echo -e "${GREEN}✅ Go service is running${NC}"
    
    # Get service info
    echo -e "${YELLOW}Service Info:${NC}"
    curl -s "$GO_SERVICE_URL/health" | python3 -m json.tool
    echo ""
else
    echo -e "${RED}❌ Go service is not running on port $GO_SERVICE_PORT${NC}"
    echo -e "${YELLOW}Please start the service first:${NC}"
    echo "   cd sanitization-config-service && ./start.sh"
    exit 1
fi

# Step 2: Test Go service endpoints
echo -e "${BLUE}Step 2: Testing Go Service Endpoints${NC}"
echo "------------------------------------"

echo -e "${YELLOW}Testing rules endpoint...${NC}"
rules_response=$(curl -s "$GO_SERVICE_URL/api/sanitization/rules")
rules_count=$(echo "$rules_response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(len(data['rules']))")
echo "   Rules available: $rules_count"

echo -e "${YELLOW}Testing service-specific rules...${NC}"
payment_rules=$(curl -s -H "X-Service-Name: payment-service" "$GO_SERVICE_URL/api/sanitization/rules")
payment_count=$(echo "$payment_rules" | python3 -c "import sys, json; data=json.load(sys.stdin); print(len(data['rules']))")
echo "   Payment service rules: $payment_count"

echo -e "${YELLOW}Testing metrics endpoint...${NC}"
metrics=$(curl -s "$GO_SERVICE_URL/metrics")
enabled_rules=$(echo "$metrics" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data['enabledRules'])")
echo "   Enabled rules: $enabled_rules"
echo ""

# Step 3: Build Java components
echo -e "${BLUE}Step 3: Building Java Components${NC}"
echo "--------------------------------"

echo -e "${YELLOW}Building filter-api...${NC}"
./gradlew :filter-api:compileJava
echo -e "${GREEN}✅ filter-api built successfully${NC}"

echo -e "${YELLOW}Building otel-extensions...${NC}"
./gradlew :otel-extensions:compileJava
echo -e "${GREEN}✅ otel-extensions built successfully${NC}"
echo ""

# Step 4: Test Java integration
echo -e "${BLUE}Step 4: Testing Java Integration${NC}"
echo "--------------------------------"

echo -e "${YELLOW}Running SanitizerRegistry tests...${NC}"
./gradlew :filter-api:test --tests "*SanitizerRegistryTest*" > /dev/null 2>&1
echo -e "${GREEN}✅ SanitizerRegistry tests passed${NC}"

echo -e "${YELLOW}Running DynamicSanitizer integration tests...${NC}"
./gradlew :filter-api:test --tests "*DynamicSanitizerIntegrationTest*" > /dev/null 2>&1
echo -e "${GREEN}✅ DynamicSanitizer integration tests passed${NC}"

echo -e "${YELLOW}Running DynamicSanitizationRuleInstaller tests...${NC}"
./gradlew :otel-extensions:test --tests "*DynamicSanitizationRuleInstallerTest*" > /dev/null 2>&1
echo -e "${GREEN}✅ DynamicSanitizationRuleInstaller tests passed${NC}"
echo ""

# Step 5: Test configuration scenarios
echo -e "${BLUE}Step 5: Testing Configuration Scenarios${NC}"
echo "---------------------------------------"

echo -e "${YELLOW}Testing dynamic sanitization configuration...${NC}"
export HT_SANITIZATION_CONFIG_ENDPOINT="$GO_SERVICE_URL/api/sanitization/rules"
export HT_SERVICE_NAME="test-service"

# Simple test to verify configuration is working
if [ -n "$HT_SANITIZATION_CONFIG_ENDPOINT" ]; then
    echo -e "${GREEN}   ✅ Dynamic endpoint configured: $HT_SANITIZATION_CONFIG_ENDPOINT${NC}"
else
    echo -e "${RED}   ❌ Dynamic endpoint not configured${NC}"
fi

if [ -n "$HT_SERVICE_NAME" ]; then
    echo -e "${GREEN}   ✅ Service name configured: $HT_SERVICE_NAME${NC}"
else
    echo -e "${RED}   ❌ Service name not configured${NC}"
fi
echo ""

# Step 6: Test rule loading
echo -e "${BLUE}Step 6: Testing Rule Loading${NC}"
echo "----------------------------"

echo -e "${YELLOW}Testing rule manager initialization...${NC}"

# Create a simple test to verify rule loading
cat > /tmp/test_rule_loading.java << 'EOF'
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;

public class test_rule_loading {
    public static void main(String[] args) {
        System.setProperty("ht.sanitization.config.endpoint", args[0]);
        System.setProperty("ht.service.name", "test-service");
        
        DynamicSanitizationRuleManager manager = new DynamicSanitizationRuleManager();
        DynamicSanitizationRuleManager.setInstance(manager);
        
        try {
            manager.start();
            Thread.sleep(2000); // Wait for initial load
            
            if (manager.getCurrentConfig() != null) {
                System.out.println("SUCCESS: Rules loaded");
                System.out.println("Config version: " + manager.getCurrentConfig().getVersion());
                System.out.println("Rules count: " + (manager.getCurrentConfig().getRules() != null ? manager.getCurrentConfig().getRules().size() : 0));
            } else {
                System.out.println("FAILED: No config loaded");
            }
        } catch (Exception e) {
            System.out.println("ERROR: " + e.getMessage());
        } finally {
            manager.stop();
        }
    }
}
EOF

# Compile and run the test
javac -cp "filter-api/build/classes/java/main:$(./gradlew :filter-api:printRuntimeClasspath -q)" /tmp/test_rule_loading.java
rule_test_output=$(java -cp "/tmp:filter-api/build/classes/java/main:$(./gradlew :filter-api:printRuntimeClasspath -q)" test_rule_loading "$GO_SERVICE_URL/api/sanitization/rules" 2>&1)

if echo "$rule_test_output" | grep -q "SUCCESS"; then
    echo -e "${GREEN}   ✅ Rule loading successful${NC}"
    echo "$rule_test_output" | grep -E "(Config version|Rules count)" | sed 's/^/   /'
else
    echo -e "${RED}   ❌ Rule loading failed${NC}"
    echo "$rule_test_output" | sed 's/^/   /'
fi

# Cleanup
rm -f /tmp/test_rule_loading.java /tmp/test_rule_loading.class
echo ""

# Step 7: Performance test
echo -e "${BLUE}Step 7: Performance Test${NC}"
echo "----------------------"

echo -e "${YELLOW}Testing Go service performance...${NC}"
start_time=$(date +%s.%N)
for i in {1..10}; do
    curl -s "$GO_SERVICE_URL/api/sanitization/rules" > /dev/null
done
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
avg_time=$(echo "scale=3; $duration / 10" | bc)
echo -e "${GREEN}   ✅ 10 requests completed in ${duration}s (avg: ${avg_time}s per request)${NC}"
echo ""

# Final summary
echo -e "${BLUE}🎉 Integration Test Summary${NC}"
echo "=========================="
echo -e "${GREEN}✅ Go service is running and responding${NC}"
echo -e "${GREEN}✅ Java components build successfully${NC}"
echo -e "${GREEN}✅ Unit tests pass${NC}"
echo -e "${GREEN}✅ Integration tests pass${NC}"
echo -e "${GREEN}✅ Configuration scenarios work${NC}"
echo -e "${GREEN}✅ Rule loading works${NC}"
echo -e "${GREEN}✅ Performance is acceptable${NC}"
echo ""
echo -e "${BLUE}🔗 Next Steps:${NC}"
echo "1. Test with a real Java application"
echo "2. Monitor logs for rule loading messages"
echo "3. Verify data sanitization in traces"
echo "4. Test rule updates and hot reloading"
echo ""
echo -e "${GREEN}Integration test completed successfully! 🎉${NC}"
