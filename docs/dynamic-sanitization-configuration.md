# Dynamic Sanitization Rule Configuration

The Hypertrace Java Agent supports dynamic loading and updating of data sanitization rules from remote endpoints. This allows you to manage sensitive data detection and sanitization rules centrally without redeploying your applications.

## Configuration

### Environment Variables / System Properties

Configure the dynamic rule manager using the following properties:

| Property | Environment Variable | Description | Default |
|----------|---------------------|-------------|---------|
| `ht.sanitization.config.endpoint` | `HT_SANITIZATION_CONFIG_ENDPOINT` | URL endpoint to fetch sanitization rules | None (disabled) |
| `ht.sanitization.config.auth.token` | `HT_SANITIZATION_CONFIG_AUTH_TOKEN` | Bearer token for authentication | None |
| `ht.sanitization.config.refresh.interval` | `HT_SANITIZATION_CONFIG_REFRESH_INTERVAL` | Refresh interval in seconds | 300 (5 minutes) |
| `ht.service.name` | `HT_SERVICE_NAME` | Service name for rule filtering | "unknown-service" |

### Example Configuration

```bash
# Enable dynamic sanitization rules
export HT_SANITIZATION_CONFIG_ENDPOINT="https://config-server.example.com/api/sanitization/rules"
export HT_SANITIZATION_CONFIG_AUTH_TOKEN="your-bearer-token"
export HT_SANITIZATION_CONFIG_REFRESH_INTERVAL="300"
export HT_SERVICE_NAME="my-service"

# Start your application with the agent
java -javaagent:hypertrace-agent.jar -jar your-app.jar
```

## Rule Configuration API

### Endpoint Response Format

The configuration endpoint should return a JSON response in the following format:

```json
{
  "version": "1.0.0",
  "timestamp": 1640995200000,
  "enabled": true,
  "markersEnabled": false,
  "markerFormat": "BRACKET",
  "rules": [
    {
      "id": "password-rule",
      "name": "Password Detection",
      "description": "Detect and sanitize password fields",
      "type": "FIELD_NAME",
      "severity": "HIGH",
      "enabled": true,
      "priority": 100,
      "fieldNames": ["password", "passwd", "pwd", "secret"],
      "maskValue": "****",
      "markerType": "PASSWORD",
      "preserveFormat": false,
      "preserveLength": 0,
      "includeServices": ["my-service"],
      "excludeServices": [],
      "conditions": {}
    },
    {
      "id": "email-pattern-rule",
      "name": "Email Pattern Detection",
      "description": "Detect email addresses using regex pattern",
      "type": "PATTERN",
      "severity": "MEDIUM",
      "enabled": true,
      "priority": 200,
      "pattern": "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
      "contentTypes": ["application/json", "application/xml"],
      "maskValue": "****@****.***",
      "markerType": "EMAIL",
      "preserveFormat": true,
      "preserveLength": 0,
      "includeServices": [],
      "excludeServices": [],
      "conditions": {}
    }
  ],
  "globalSettings": {
    "defaultMaskValue": "****",
    "enableLogging": true,
    "logLevel": "INFO"
  }
}
```

### Rule Types

1. **FIELD_NAME**: Match based on field names in JSON/XML/form data
2. **PATTERN**: Match using regular expressions
3. **CONTENT_TYPE**: Apply rules based on content type
4. **CUSTOM**: Custom rule implementation

### Severity Levels

- **LOW**: Informational data
- **MEDIUM**: Potentially sensitive data
- **HIGH**: Sensitive data (passwords, tokens)
- **CRITICAL**: Highly sensitive data (SSNs, credit cards)

## Integration Lifecycle

### Startup Process

1. **Agent Initialization**: The `DynamicSanitizationRuleInstaller` runs during agent startup
2. **Rule Manager Creation**: A singleton `DynamicSanitizationRuleManager` is created
3. **Initial Rule Loading**: Rules are fetched immediately from the configured endpoint
4. **Periodic Updates**: Rules are refreshed at the configured interval
5. **Shutdown Handling**: The rule manager is properly stopped during application shutdown

### Rule Application

1. **Filter Integration**: The `DynamicSensitiveDataSanitizer` uses the rule manager
2. **Rule Retrieval**: Current rules are fetched from the singleton instance
3. **Fallback Behavior**: If dynamic rules are unavailable, falls back to static configuration
4. **Performance**: Rules are cached and compiled patterns are reused

## Monitoring and Logging

The dynamic rule manager provides detailed logging:

```
INFO  - Dynamic sanitization rule manager configured: endpoint=https://..., refreshInterval=PT5M
INFO  - Started dynamic sanitization rule manager with refresh interval: PT5M
INFO  - Successfully updated sanitization rules: version=1.0.0, rules=5
INFO  - Config version changed: 1.0.0 -> 1.0.1
INFO  - Rule count changed: 5 -> 7
WARN  - Failed to fetch sanitization config: HTTP 404
ERROR - Error loading sanitization rules from endpoint: Connection timeout
```

## Security Considerations

1. **Authentication**: Use bearer tokens to secure the configuration endpoint
2. **HTTPS**: Always use HTTPS for the configuration endpoint
3. **Validation**: Rules are validated before being applied
4. **Fallback**: System falls back to static configuration if dynamic loading fails
5. **Error Handling**: Network errors don't crash the application

## Troubleshooting

### Common Issues

1. **Rules not loading**: Check endpoint URL and authentication token
2. **High memory usage**: Reduce refresh interval or optimize rule patterns
3. **Performance impact**: Monitor rule complexity and pattern compilation
4. **Network timeouts**: Adjust timeout settings or check network connectivity

### Debug Configuration

Enable debug logging to troubleshoot issues:

```bash
export HT_LOG_LEVEL=DEBUG
```

This will provide detailed information about rule loading, validation, and application.
