# 在 Instrumentation 中使用数据脱敏

本文档展示如何在 Hypertrace Java Agent 的 instrumentation 代码中使用数据脱敏功能。

## 示例：在 Servlet Instrumentation 中使用数据脱敏

以下是如何修改现有的 instrumentation 代码来添加数据脱敏支持的示例。

### 1. 添加依赖

首先，确保您的 instrumentation 模块依赖了 `filter-api` 模块。在 `build.gradle.kts` 中添加：

```kotlin
dependencies {
    implementation(project(":filter-api"))
    // 其他依赖...
}
```

### 2. 在捕获请求体时应用脱敏

修改捕获请求体的代码，例如在 Servlet instrumentation 中：

```java
import org.hypertrace.agent.filter.utils.DataSanitizationUtils;
import org.hypertrace.agent.core.instrumentation.HypertraceSemanticAttributes;

// 在捕获请求体后，添加脱敏处理
public static void captureRequestBody(Span span, String body, Map<String, String> headers) {
    if (body != null && !body.isEmpty()) {
        // 使用 DataSanitizationUtils 来添加脱敏后的数据到 span
        DataSanitizationUtils.addSanitizedAttribute(
            span,
            HypertraceSemanticAttributes.HTTP_REQUEST_BODY,
            body,
            headers
        );
    }
}
```

### 3. 在捕获响应体时应用脱敏

类似地，对响应体也进行脱敏：

```java
public static void captureResponseBody(Span span, String body, Map<String, String> headers) {
    if (body != null && !body.isEmpty()) {
        DataSanitizationUtils.addSanitizedAttribute(
            span,
            HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
            body,
            headers
        );
    }
}
```

### 4. 手动脱敏数据

如果需要手动脱敏数据（而不是直接添加到 span），可以使用：

```java
String sanitizedBody = DataSanitizationUtils.sanitize(originalBody, headers);
// 然后使用 sanitizedBody 进行后续处理
```

## 完整示例：修改现有的 Servlet Instrumentation

假设您要修改 `ServletAndFilterInstrumentation` 来支持数据脱敏：

```java
package io.opentelemetry.javaagent.instrumentation.hypertrace.servlet;

import io.opentelemetry.api.trace.Span;
import org.hypertrace.agent.filter.utils.DataSanitizationUtils;
import org.hypertrace.agent.core.instrumentation.HypertraceSemanticAttributes;
import java.util.Map;
import java.util.HashMap;

public class ServletBodyCaptureAdvice {
    
    @Advice.OnMethodExit(suppress = Throwable.class)
    public static void onExit(
        @Advice.This HttpServletRequest request,
        @Advice.Local("currentSpan") Span span) {
        
        // 获取请求体
        String body = getRequestBody(request);
        
        if (body != null && !body.isEmpty()) {
            // 构建 headers map
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", request.getContentType());
            
            // 使用数据脱敏工具添加属性
            DataSanitizationUtils.addSanitizedAttribute(
                span,
                HypertraceSemanticAttributes.HTTP_REQUEST_BODY,
                body,
                headers
            );
        }
    }
}
```

## 注意事项

1. **性能考虑**：数据脱敏会增加一定的处理开销，特别是对于大型 JSON/XML 文档。确保只在必要时启用。

2. **配置检查**：数据脱敏功能可以通过配置禁用。工具类会自动检查配置状态。

3. **错误处理**：如果脱敏过程中发生错误，`DataSanitizationUtils` 会记录警告并返回原始数据（或不添加属性），以避免影响正常的追踪功能。

4. **内容类型**：脱敏功能会根据内容类型（Content-Type）来决定如何处理数据。支持的内容类型包括：
   - application/json
   - application/xml
   - text/xml
   - application/x-www-form-urlencoded
   - text/plain

5. **Headers 参数**：始终传递包含 Content-Type 的 headers map，这样脱敏器可以选择合适的处理策略。

## 测试您的修改

1. 启用数据脱敏：
   ```bash
   java -Dht.data.sanitization.enabled=true -javaagent:hypertrace-agent.jar YourApp
   ```

2. 发送包含敏感数据的请求：
   ```bash
   curl -X POST http://localhost:8080/api/user \
     -H "Content-Type: application/json" \
     -d '{"username":"<EMAIL>","password":"secret123","credit_card":"4111-1111-1111-1111"}'
   ```

3. 在追踪数据中验证敏感数据已被脱敏：
   - password 应显示为 "****"
   - credit_card 应显示为 "****"
   - email 应显示为 "****@example.com"

## 扩展脱敏规则

如果默认的脱敏规则不满足需求，可以：

1. 修改 `DefaultSensitiveDataSanitizer` 中的 `SENSITIVE_FIELDS` 集合
2. 添加新的正则表达式模式
3. 创建自定义的 `SensitiveDataSanitizer` 实现

记住在修改后重新编译和测试您的 agent。
