# Dynamic Sanitization Rules Example

This example demonstrates how to configure and use dynamic sanitization rules with the Hypertrace Java Agent.

## Configuration Server Setup

First, set up a configuration server that provides sanitization rules. Here's a simple example using Spring Boot:

```java
@RestController
@RequestMapping("/api/sanitization")
public class SanitizationConfigController {

    @GetMapping("/rules")
    public SanitizationConfig getRules(@RequestHeader(value = "X-Service-Name", required = false) String serviceName) {
        SanitizationConfig config = new SanitizationConfig();
        config.setVersion("1.0.0");
        config.setTimestamp(System.currentTimeMillis());
        config.setEnabled(true);
        config.setMarkersEnabled(false);
        config.setMarkerFormat("BRACKET");

        List<SanitizationRule> rules = new ArrayList<>();

        // Password field rule
        SanitizationRule passwordRule = new SanitizationRule("password-rule", "Password Detection", SanitizationRule.RuleType.FIELD_NAME);
        passwordRule.setSeverity(SanitizationRule.SeverityLevel.HIGH);
        passwordRule.setEnabled(true);
        passwordRule.setPriority(100);
        passwordRule.setFieldNames(Arrays.asList("password", "passwd", "pwd", "secret", "token"));
        passwordRule.setMaskValue("****");
        passwordRule.setMarkerType("PASSWORD");
        rules.add(passwordRule);

        // Email pattern rule
        SanitizationRule emailRule = new SanitizationRule("email-pattern", "Email Pattern Detection", SanitizationRule.RuleType.PATTERN);
        emailRule.setSeverity(SanitizationRule.SeverityLevel.MEDIUM);
        emailRule.setEnabled(true);
        emailRule.setPriority(200);
        emailRule.setPattern("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");
        emailRule.setContentTypes(Arrays.asList("application/json", "application/xml"));
        emailRule.setMaskValue("****@****.***");
        emailRule.setMarkerType("EMAIL");
        emailRule.setPreserveFormat(true);
        rules.add(emailRule);

        // Credit card rule
        SanitizationRule creditCardRule = new SanitizationRule("credit-card", "Credit Card Detection", SanitizationRule.RuleType.PATTERN);
        creditCardRule.setSeverity(SanitizationRule.SeverityLevel.CRITICAL);
        creditCardRule.setEnabled(true);
        creditCardRule.setPriority(50);
        creditCardRule.setPattern("\\b(?:\\d{4}[-\\s]?){3}\\d{4}\\b");
        creditCardRule.setMaskValue("****-****-****-****");
        creditCardRule.setMarkerType("CREDIT_CARD");
        rules.add(creditCardRule);

        // Service-specific rule
        if ("payment-service".equals(serviceName)) {
            SanitizationRule paymentRule = new SanitizationRule("payment-data", "Payment Data", SanitizationRule.RuleType.FIELD_NAME);
            paymentRule.setSeverity(SanitizationRule.SeverityLevel.HIGH);
            paymentRule.setEnabled(true);
            paymentRule.setPriority(75);
            paymentRule.setFieldNames(Arrays.asList("accountNumber", "routingNumber", "cvv"));
            paymentRule.setMaskValue("***");
            paymentRule.setIncludeServices(Arrays.asList("payment-service"));
            rules.add(paymentRule);
        }

        config.setRules(rules);

        Map<String, Object> globalSettings = new HashMap<>();
        globalSettings.put("defaultMaskValue", "****");
        globalSettings.put("enableLogging", true);
        globalSettings.put("logLevel", "INFO");
        config.setGlobalSettings(globalSettings);

        return config;
    }
}
```

## Application Configuration

Configure your application to use dynamic sanitization rules:

### Environment Variables

```bash
# Configuration endpoint
export HT_SANITIZATION_CONFIG_ENDPOINT="https://config-server.example.com/api/sanitization/rules"

# Authentication token (optional)
export HT_SANITIZATION_CONFIG_AUTH_TOKEN="your-bearer-token"

# Refresh interval (default: 300 seconds)
export HT_SANITIZATION_CONFIG_REFRESH_INTERVAL="300"

# Service name for rule filtering
export HT_SERVICE_NAME="payment-service"

# Other Hypertrace configuration
export HT_REPORTING_ENDPOINT="http://localhost:4317"
export HT_SERVICE_NAME="payment-service"
```

### System Properties

```bash
java -javaagent:hypertrace-agent.jar \
     -Dht.sanitization.config.endpoint=https://config-server.example.com/api/sanitization/rules \
     -Dht.sanitization.config.auth.token=your-bearer-token \
     -Dht.sanitization.config.refresh.interval=300 \
     -Dht.service.name=payment-service \
     -Dht.reporting.endpoint=http://localhost:4317 \
     -jar your-application.jar
```

## Testing the Configuration

### Sample Application

```java
@RestController
public class TestController {

    @PostMapping("/api/user")
    public ResponseEntity<String> createUser(@RequestBody String userJson) {
        // This request body will be automatically sanitized based on the rules
        // Fields like "password", "email" will be masked according to the rules
        
        return ResponseEntity.ok("User created");
    }

    @PostMapping("/api/payment")
    public ResponseEntity<String> processPayment(@RequestBody String paymentJson) {
        // Payment-specific fields will be sanitized based on service-specific rules
        
        return ResponseEntity.ok("Payment processed");
    }
}
```

### Sample Request

```bash
curl -X POST http://localhost:8080/api/user \
     -H "Content-Type: application/json" \
     -d '{
       "username": "john.doe",
       "email": "<EMAIL>",
       "password": "secretpassword123",
       "phone": "******-123-4567"
     }'
```

### Expected Sanitized Output in Traces

Without markers:
```json
{
  "username": "john.doe",
  "email": "****@****.***",
  "password": "****",
  "phone": "******-123-4567"
}
```

With markers enabled:
```json
{
  "username": "john.doe",
  "email": "[EMAIL:****@****.***]",
  "password": "[PASSWORD:****]",
  "phone": "******-123-4567"
}
```

## Monitoring and Troubleshooting

### Log Output

The agent will log information about rule loading and application:

```
INFO  - Dynamic sanitization rule manager configured: endpoint=https://config-server.example.com/api/sanitization/rules, refreshInterval=PT5M
INFO  - Started dynamic sanitization rule manager with refresh interval: PT5M
INFO  - Successfully updated sanitization rules: version=1.0.0, rules=4
INFO  - Config version changed: 1.0.0 -> 1.0.1
INFO  - Rule count changed: 4 -> 5
```

### Health Check

You can verify that rules are being loaded by checking the application logs or implementing a health check endpoint in your configuration server.

### Fallback Behavior

If the configuration server is unavailable, the agent will:
1. Continue using the last successfully loaded rules
2. Fall back to static configuration if no rules were ever loaded
3. Log warnings about the inability to fetch new rules
4. Continue to retry at the configured interval

This ensures that your application continues to function even if the configuration server is temporarily unavailable.
