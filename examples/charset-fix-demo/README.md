# Hypertrace Java Agent 字符编码修复演示

这个示例演示了 Hypertrace Java Agent 字符编码问题的修复效果。

## 问题描述

在修复前，使用 Hypertrace Java Agent 监控 Spring Boot 应用时会出现：

1. **响应头异常**：多返回 `http.response.header.content-encoding:ISO-8859-1`
   - 这是因为 Agent 错误地将字符编码（character encoding）设置为内容编码（content encoding）
   - 字符编码和内容编码是两个完全不同的概念
2. **中文乱码**：响应体中的中文字符显示为乱码

## 修复内容

### 1. 默认字符编码修改

```java
// 修改前
private static final Charset DEFAULT_CHARSET = StandardCharsets.ISO_8859_1;

// 修改后
private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
```

### 2. Spring Boot 支持增强

新增了针对 Spring Boot 应用的字符编码检测：

```java
public static Charset toCharsetWithSpringBootSupport(String charsetName, String contentType) {
    if (charsetName == null || charsetName.isEmpty()) {
        // 对于 JSON 内容类型，优先使用 UTF-8
        if (contentType != null && contentType.toLowerCase().contains("json")) {
            return StandardCharsets.UTF_8;
        }
        return DEFAULT_CHARSET;
    }
    // 其他逻辑保持不变
}
```

### 3. 配置选项支持

支持通过系统属性自定义默认字符编码：

```bash
# 使用 UTF-8（默认）
-Dhypertrace.agent.default.charset=UTF-8

# 如需使用 ISO-8859-1
-Dhypertrace.agent.default.charset=ISO-8859-1
```

## 测试验证

### 测试用例

```java
@Test
void testDefaultCharsetIsUtf8() {
    // 验证默认字符集现在是 UTF-8
    Assertions.assertEquals(
        StandardCharsets.UTF_8, 
        ContentTypeCharsetUtils.getDefaultCharset());
}

@Test
void testSpringBootCharsetSupport() {
    // 验证 JSON 内容类型默认使用 UTF-8
    Assertions.assertEquals(
        StandardCharsets.UTF_8,
        ContentTypeCharsetUtils.toCharsetWithSpringBootSupport(null, "application/json"));
    
    // 验证明确指定的字符编码被尊重
    Assertions.assertEquals(
        StandardCharsets.ISO_8859_1,
        ContentTypeCharsetUtils.toCharsetWithSpringBootSupport("ISO-8859-1", "application/json"));
}
```

## 使用方法

### 1. 默认使用（推荐）

直接使用修复后的 Agent，无需额外配置：

```bash
java -javaagent:hypertrace-agent.jar -jar your-spring-boot-app.jar
```

### 2. 自定义字符编码

如果需要特定的字符编码：

```bash
java -javaagent:hypertrace-agent.jar \
     -Dhypertrace.agent.default.charset=UTF-8 \
     -jar your-spring-boot-app.jar
```

## 验证修复效果

### 1. 检查响应头

修复前：
```
http.response.header.content-encoding: ISO-8859-1
```

修复后：
```
# 不再出现异常的 content-encoding 头
```

### 2. 检查中文字符

修复前：
```json
{"message": "ä¸­æ–‡æµ‹è¯•"}
```

修复后：
```json
{"message": "中文测试"}
```

## 兼容性说明

- ✅ 向后兼容，不影响现有应用
- ✅ 应用明确指定字符编码时，优先使用应用指定的编码
- ✅ 只在没有明确指定字符编码时使用新的默认行为
- ✅ 支持通过系统属性恢复原有行为

## 适用场景

- Spring Boot 应用
- 返回 JSON 数据的 REST API
- 包含中文内容的 Web 应用
- 需要 UTF-8 编码支持的现代 Web 应用
