service_name: service_name
reporting:
  endpoint: http://localhost:4317

# 数据捕获配置
data_capture:
  # 启用 HTTP 请求和响应体的捕获
  http_body:
    request: true
    response: true
  
  # 配置允许捕获的内容类型
  allowed_content_types:
    - application/json
    - application/xml
    - text/xml
    - application/x-www-form-urlencoded
    - text/plain

# 敏感数据脱敏配置
# 可以通过环境变量或系统属性控制
# -Dht.data.sanitization.enabled=true (默认启用)
# 或者设置环境变量 HT_DATA_SANITIZATION_ENABLED=true

# 禁用特定的 filter provider（如果需要）
# -Dht.filter.provider.MockFilterProvider.disabled=true
