# Hypertrace Java Agent 字符编码问题完整修复总结

## 🔍 问题根本原因

您遇到的问题实际上包含两个相关但不同的问题：

### 问题1：错误的 Content-Encoding 响应头
Spring Boot 应用本身没有设置 `Content-Encoding` 响应头，但 Hypertrace Java Agent 错误地添加了 `http.response.header.content-encoding:ISO-8859-1`。

**根本原因是概念混淆**：
- **字符编码（Character Encoding）**：如 UTF-8, ISO-8859-1，用于文本字符的编码
- **内容编码（Content Encoding）**：如 gzip, deflate, br，用于内容压缩

Agent 错误地将 `httpResponse.getCharacterEncoding()` 的值设置为 `Content-Encoding` 响应头。

### 问题2：响应体中文乱码
Jaeger 中存储的 `http.response.body` 数据出现中文乱码，如：
```json
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}
```

**根本原因是时序问题**：
- Buffer 创建时使用的字符编码与响应完成时的实际字符编码不一致
- Spring Boot 在处理过程中设置字符编码，但 Agent 在更早的时机就固定了编码

## 🛠️ 修复方案

### 修复1：移除错误的 Content-Encoding 设置

**修改前的错误代码**：
```java
// 在 Servlet50AndFilterInstrumentation.java 和 Servlet30AndFilterInstrumentation.java 中
if (httpResponse.getCharacterEncoding() != null
    && !httpResponse.getCharacterEncoding().isEmpty()) {
  currentSpan.setAttribute(
      HypertraceSemanticAttributes.HTTP_RESPONSE_HEADER_CONTENT_ENCODING,
      httpResponse.getCharacterEncoding());
}
```

**修复后**：
```java
// Note: httpResponse.getCharacterEncoding() returns character encoding (e.g., UTF-8),
// not content encoding (e.g., gzip). Content-Encoding header should only be set
// if the response is actually compressed. We should not automatically set this
// based on character encoding as they are different concepts.
```

### 修复2：响应体字符编码时序修复

**核心问题**：在响应体捕获完成时，重新检查当前的字符编码。

```java
// 修改前的问题代码
span.setAttribute(
    HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
    DataSanitizationUtils.sanitize(buffer.toStringWithSuppliedCharset()));

// 修复后的代码
try {
  // Re-check the character encoding at response completion time
  String currentCharsetStr = httpServletResponse.getCharacterEncoding();
  String contentType = httpServletResponse.getContentType();
  Charset currentCharset = ContentTypeCharsetUtils.toCharsetWithSpringBootSupport(currentCharsetStr, contentType);

  // Convert the buffer content using the current (correct) charset
  String responseBody = new String(buffer.toByteArray(), currentCharset);

  span.setAttribute(
      HypertraceSemanticAttributes.HTTP_RESPONSE_BODY,
      DataSanitizationUtils.sanitize(responseBody));
} catch (Exception e) {
  // Fallback to original method
}
```

### 修复3：辅助字符编码改进

1. **默认字符编码改为 UTF-8**：更适合中文应用
2. **添加 Spring Boot 支持**：针对 JSON 内容类型优先使用 UTF-8
3. **添加配置选项**：支持通过系统属性自定义默认字符编码

## 📁 修改的文件

1. **修复1 - 移除错误的 Content-Encoding**：
   - `instrumentation/servlet/servlet-5.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v5_0/Servlet50AndFilterInstrumentation.java`
   - `instrumentation/servlet/servlet-3.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v3_0/nowrapping/Servlet30AndFilterInstrumentation.java`

2. **修复2 - 响应体字符编码时序修复**：
   - `instrumentation/servlet/servlet-5.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v5_0/Utils.java`
   - `instrumentation/servlet/servlet-3.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v3_0/nowrapping/Utils.java`

3. **修复3 - 辅助字符编码改进**：
   - `javaagent-core/src/main/java/org/hypertrace/agent/core/instrumentation/utils/ContentTypeCharsetUtils.java`
   - 相关的 servlet 响应处理代码

## ✅ 修复效果

### 修复前
```
# 问题1：错误的响应头
http.response.header.content-encoding: ISO-8859-1

# 问题2：Jaeger 中的响应体乱码
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}
```

### 修复后
```
# 问题1：不再有错误的 content-encoding 头
# （只有在实际压缩时才会有正确的 Content-Encoding 头）

# 问题2：Jaeger 中的响应体正常显示
{"id":5,"name":"测试用户","email":"****@example.com","phone":"****"}
```

## 🚀 使用方法

**默认使用（推荐）**：
```bash
java -javaagent:hypertrace-agent.jar -jar your-spring-boot-app.jar
```

**自定义字符编码（如需要）**：
```bash
java -javaagent:hypertrace-agent.jar \
     -Dhypertrace.agent.default.charset=UTF-8 \
     -jar your-spring-boot-app.jar
```

## 🎯 关键要点

1. **主要问题**：Agent 错误地将字符编码设置为内容编码响应头
2. **修复方式**：移除错误的逻辑，不再自动设置 Content-Encoding
3. **向后兼容**：修复不会影响现有应用的正常运行
4. **正确行为**：Content-Encoding 只应在实际压缩时由 HTTP 服务器设置

## 📝 技术说明

- **字符编码**：处理文本字符的编码方式（UTF-8, GBK, ISO-8859-1 等）
- **内容编码**：处理 HTTP 响应体的压缩方式（gzip, deflate, br 等）
- **HTTP 规范**：Content-Encoding 头只应在响应体被压缩时设置
- **Spring Boot**：通常不会自动设置 Content-Encoding，除非配置了压缩

这个修复解决了 Agent 的概念混淆问题，确保了正确的 HTTP 语义和中文字符的正常显示。
