name: release
on:
  push:
    tags: ['[0-9]+\.[0-9]+\.[0-9]+']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      # Set fetch-depth: 0 to fetch commit history and tags for use in version calculation
      - name: Check out code
        uses: actions/checkout@v2.3.4
        with:
          fetch-depth: 0
          submodules: true

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: create checksum file
        uses: hypertrace/github-actions/checksum@main

      - name: Cache packages
        id: cache-packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle
          key: gradle-packages-${{ runner.os }}-${{ github.job }}-${{ hashFiles('**/checksum.txt') }}
          restore-keys: |
            gradle-packages-${{ runner.os }}-${{ github.job }}
            gradle-packages-${{ runner.os }}

      - name: build
        run: make build
        env:
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED
          TERM: dumb

  smoke-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        suite: [ "glassfish", "jetty", "liberty", "tomcat", "tomee", "wildfly", "other" ]
      fail-fast: true
    steps:
      # Set fetch-depth: 0 to fetch commit history and tags for use in version calculation
      - name: Check out code
        uses: actions/checkout@v2.3.4
        with:
          fetch-depth: 0
          submodules: true

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: create checksum file
        uses: hypertrace/github-actions/checksum@main

      - name: Cache packages
        id: cache-packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle
          key: gradle-packages-${{ runner.os }}-${{ github.job }}-${{ hashFiles('**/checksum.txt') }}
          restore-keys: |
            gradle-packages-${{ runner.os }}-${{ github.job }}
            gradle-packages-${{ runner.os }}

      - name: smoke-test
        run: make smoke-test SMOKE_TEST_SUITE=${{ matrix.suite }}
        env:
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED
          TERM: dumb

  release:
    needs: [ test, smoke-test ]
    runs-on: ubuntu-latest
    steps:
      # Set fetch-depth: 0 to fetch commit history and tags for use in version calculation
      - name: Check out code
        uses: actions/checkout@v2.3.4
        with:
          fetch-depth: 0
          submodules: true

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: create checksum file
        uses: hypertrace/github-actions/checksum@main

      - name: Cache packages
        id: cache-packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle
          key: gradle-packages-${{ runner.os }}-${{ github.job }}-${{ hashFiles('**/checksum.txt') }}
          restore-keys: |
            gradle-packages-${{ runner.os }}-${{ github.job }}
            gradle-packages-${{ runner.os }}

      - name: Release jars
        run: |
          echo "Releasing version:" && ./gradlew printVersion clean
          ORG_GRADLE_PROJECT_signingKey=$(echo $SIGNING_KEY | base64 -d) ./gradlew publish
          ./gradlew closeAndReleaseRepository
        env:
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED
          TERM: dumb
          ORG_GRADLE_PROJECT_ossrhUsername: ${{ secrets.ORG_GRADLE_PROJECT_OSSRHUSERNAME }}
          ORG_GRADLE_PROJECT_ossrhPassword: ${{ secrets.ORG_GRADLE_PROJECT_OSSRHPASSWORD }}
          ORG_GRADLE_PROJECT_signingKeyId: ${{ secrets.ORG_GRADLE_PROJECT_SIGNINGKEYID }}
          ORG_GRADLE_PROJECT_signingPassword: ${{ secrets.ORG_GRADLE_PROJECT_SIGNINGPASSWORD }}
          SIGNING_KEY: ${{ secrets.MAVEN_SIGNING_KEY }}

      - name: Release docker image
        run: |
          echo $DOCKER_PASSWORD | docker login --username $DOCKER_USERNAME --password-stdin
          DOCKER_TAG=${GITHUB_REF##*/} make docker docker-push
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKERHUB_PUBLISH_USER }}
          DOCKER_PASSWORD: ${{ secrets.DOCKERHUB_PUBLISH_TOKEN }}

      - name: Upload jars to release page
        run: |
          export TAG=${GITHUB_REF##*/}
          gh release create ${TAG} --title "Release ${TAG}" javaagent/build/libs/*-all.jar
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
