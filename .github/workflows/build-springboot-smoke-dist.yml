name: Build Spring Boot smoke test image

on:
  push:
    paths:
      - 'smoke-tests/springboot/**'
      - '.github/workflows/build-springboot-smoke-dist.yml'
    branches:
      - main
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          fetch-depth: 0

      - name: Set up JDK 11 for running Gradle
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: Cache gradle dependencies
        uses: burrunan/gradle-cache-action@v1.10
        with:
          job-id: springboot-smoke

      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_PUBLISH_USER }}
          password: ${{ secrets.DOCKERHUB_PUBLISH_TOKEN }}

      - name: Build Docker Image
        run: |
          TAG="$(date '+%Y%m%d').$GITHUB_RUN_ID"
          echo "Pushing to tag $TAG"
          ./gradlew jib -PtargetJDK=8 -Djib.httpTimeout=120000 -Djib.console=plain --stacktrace -Ptag=$TAG
          ./gradlew jib -PtargetJDK=11 -Djib.httpTimeout=120000 -Djib.console=plain --stacktrace -Ptag=$TAG
          ./gradlew jib -PtargetJDK=15 -Djib.httpTimeout=120000 -Djib.console=plain --stacktrace -Ptag=$TAG
        working-directory: smoke-tests/springboot
        env:
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED
