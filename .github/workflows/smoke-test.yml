name: smoke-test
on:
  push:
    branches:
      - main
    paths-ignore:
      - '**/README.md'
      - 'smoke-tests/matrix'
  pull_request:

jobs:
  smoke-test:
    runs-on: ubuntu-latest
    strategy: 
      matrix:
        suite: [ "glassfish", "jetty", "liberty", "tomcat", "tomee", "wildfly", "other" ]
      fail-fast: true  
    steps:
       # Set fetch-depth: 0 to fetch commit history and tags for use in version calculation
      - name: Check out code
        uses: actions/checkout@v2.3.4
        with:
          ref: ${{github.event.pull_request.head.ref}}
          repository: ${{github.event.pull_request.head.repo.full_name}}
          fetch-depth: 0
          submodules: true

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: create checksum file
        uses: hypertrace/github-actions/checksum@main

      - name: Cache packages
        id: cache-packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle
          key: gradle-packages-${{ runner.os }}-${{ github.job }}-${{ hashFiles('**/checksum.txt') }}
          restore-keys: |
            gradle-packages-${{ runner.os }}-${{ github.job }}
            gradle-packages-${{ runner.os }}

      - name: smoke-test
        run: make smoke-test SMOKE_TEST_SUITE=${{ matrix.suite }}
        env: 
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED
          TERM: dumb
