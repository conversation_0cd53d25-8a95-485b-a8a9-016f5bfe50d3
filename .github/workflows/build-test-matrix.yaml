name: Build test matrix images

on:
  push:
    paths:
      - 'smoke-tests/matrix/**'
      - '.github/workflows/build-test-matrix.yaml'
    branches: 'main'
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 11 for running Gradle
        uses: actions/setup-java@v2
        with:
          distribution: adopt
          java-version: 11

      - name: Cache gradle dependencies
        uses: burrunan/gradle-cache-action@v1.6
        with:
          job-id: matrix-smoke

      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_PUBLISH_USER }}
          password: ${{ secrets.DOCKERHUB_PUBLISH_TOKEN }}

      - name: Build Docker Image
        run: |
          TAG="$(date '+%Y%m%d').$GITHUB_RUN_ID"
          echo "Using extra tag $TAG"
          ./gradlew buildMatrix pushMatrix -PextraTag=$TAG
        working-directory: smoke-tests/matrix
        env:
          JVM_OPTS: -Xmx1g --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED