/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.config;

import java.util.List;
import java.util.Map;

/** Represents a sanitization rule that can be applied to sensitive data */
public class SanitizationRule {

  public enum RuleType {
    FIELD_NAME, // 基于字段名称的规则
    PATTERN, // 基于正则表达式模式的规则
    CONTENT_TYPE, // 基于内容类型的规则
    CUSTOM // 自定义规则
  }

  public enum SeverityLevel {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
  }

  private String id;
  private String name;
  private String description;
  private RuleType type;
  private SeverityLevel severity;
  private boolean enabled;
  private int priority;

  // 规则匹配条件
  private List<String> fieldNames; // 字段名称列表（用于 FIELD_NAME 类型）
  private String pattern; // 正则表达式模式（用于 PATTERN 类型）
  private List<String> contentTypes; // 内容类型列表

  // 脱敏配置
  private String maskValue; // 脱敏掩码值，默认 "****"
  private String markerType; // 标记类型，如 "PASSWORD", "EMAIL" 等
  private boolean preserveFormat; // 是否保留格式（如邮箱保留域名）
  private int preserveLength; // 保留的字符长度

  // 应用条件
  private List<String> includeServices; // 仅对指定服务生效
  private List<String> excludeServices; // 排除的服务
  private Map<String, String> conditions; // 额外的条件

  // 构造函数
  public SanitizationRule() {}

  public SanitizationRule(String id, String name, RuleType type) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.enabled = true;
    this.priority = 100;
    this.maskValue = "****";
    this.severity = SeverityLevel.MEDIUM;
  }

  // Getters and Setters
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public RuleType getType() {
    return type;
  }

  public void setType(RuleType type) {
    this.type = type;
  }

  public SeverityLevel getSeverity() {
    return severity;
  }

  public void setSeverity(SeverityLevel severity) {
    this.severity = severity;
  }

  public boolean isEnabled() {
    return enabled;
  }

  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }

  public int getPriority() {
    return priority;
  }

  public void setPriority(int priority) {
    this.priority = priority;
  }

  public List<String> getFieldNames() {
    return fieldNames;
  }

  public void setFieldNames(List<String> fieldNames) {
    this.fieldNames = fieldNames;
  }

  public String getPattern() {
    return pattern;
  }

  public void setPattern(String pattern) {
    this.pattern = pattern;
  }

  public List<String> getContentTypes() {
    return contentTypes;
  }

  public void setContentTypes(List<String> contentTypes) {
    this.contentTypes = contentTypes;
  }

  public String getMaskValue() {
    return maskValue;
  }

  public void setMaskValue(String maskValue) {
    this.maskValue = maskValue;
  }

  public String getMarkerType() {
    return markerType;
  }

  public void setMarkerType(String markerType) {
    this.markerType = markerType;
  }

  public boolean isPreserveFormat() {
    return preserveFormat;
  }

  public void setPreserveFormat(boolean preserveFormat) {
    this.preserveFormat = preserveFormat;
  }

  public int getPreserveLength() {
    return preserveLength;
  }

  public void setPreserveLength(int preserveLength) {
    this.preserveLength = preserveLength;
  }

  public List<String> getIncludeServices() {
    return includeServices;
  }

  public void setIncludeServices(List<String> includeServices) {
    this.includeServices = includeServices;
  }

  public List<String> getExcludeServices() {
    return excludeServices;
  }

  public void setExcludeServices(List<String> excludeServices) {
    this.excludeServices = excludeServices;
  }

  public Map<String, String> getConditions() {
    return conditions;
  }

  public void setConditions(Map<String, String> conditions) {
    this.conditions = conditions;
  }

  @Override
  public String toString() {
    return "SanitizationRule{"
        + "id='"
        + id
        + '\''
        + ", name='"
        + name
        + '\''
        + ", type="
        + type
        + ", severity="
        + severity
        + ", enabled="
        + enabled
        + ", priority="
        + priority
        + '}';
  }
}
