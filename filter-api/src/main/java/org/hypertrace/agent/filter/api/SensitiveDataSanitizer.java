/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.api;

import java.util.Map;

/** Interface for sanitizing sensitive data in request/response bodies */
public interface SensitiveDataSanitizer {

  /**
   * Sanitize sensitive data from the given text
   *
   * @param input the input text to sanitize
   * @param headers request/response headers for context
   * @return sanitized text
   */
  String sanitize(String input, Map<String, String> headers);

  /**
   * Check if sanitization is needed based on content type
   *
   * @param contentType the content type
   * @return true if sanitization is needed
   */
  boolean shouldSanitize(String contentType);
}
