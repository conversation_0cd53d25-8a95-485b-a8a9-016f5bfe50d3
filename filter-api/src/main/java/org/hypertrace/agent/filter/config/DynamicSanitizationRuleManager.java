/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Manager for dynamically loading and updating sanitization rules from remote endpoints */
public class DynamicSanitizationRuleManager {

  private static final Logger logger =
      LoggerFactory.getLogger(DynamicSanitizationRuleManager.class);

  // Singleton instance for global access
  private static volatile DynamicSanitizationRuleManager instance;

  private final ObjectMapper objectMapper;
  private final ScheduledExecutorService scheduler;
  private final AtomicReference<SanitizationConfig> currentConfig;

  private String configEndpoint;
  private Duration refreshInterval;
  private String authToken;
  private String serviceName;
  private int connectTimeoutMs = 10000; // 10 seconds
  private int readTimeoutMs = 30000; // 30 seconds

  public DynamicSanitizationRuleManager() {
    this.objectMapper = new ObjectMapper();
    this.scheduler =
        Executors.newSingleThreadScheduledExecutor(
            r -> {
              Thread t = new Thread(r, "sanitization-rule-updater");
              t.setDaemon(true);
              return t;
            });
    this.currentConfig = new AtomicReference<>(new SanitizationConfig());

    // 从系统属性或环境变量读取配置
    loadConfiguration();
  }

  /** 从系统属性或环境变量加载配置 */
  private void loadConfiguration() {
    configEndpoint = getProperty("ht.sanitization.config.endpoint");
    authToken = getProperty("ht.sanitization.config.auth.token");
    serviceName = getProperty("ht.service.name", "unknown-service");

    String refreshIntervalStr = getProperty("ht.sanitization.config.refresh.interval", "300");
    try {
      refreshInterval = Duration.ofSeconds(Long.parseLong(refreshIntervalStr));
    } catch (NumberFormatException e) {
      refreshInterval = Duration.ofSeconds(300); // 默认5分钟
    }

    logger.info(
        "Dynamic sanitization rule manager configured: endpoint={}, refreshInterval={}",
        configEndpoint,
        refreshInterval);
  }

  /** 启动动态规则更新 */
  public void start() {
    // Use both logger and System.out to ensure visibility in agent environment
    logger.info("DynamicSanitizationRuleManager.start() called");
    System.out.println("[HT-Agent] DynamicSanitizationRuleManager.start() called");

    logger.info("configEndpoint: {}", configEndpoint);
    System.out.println("[HT-Agent] configEndpoint: " + configEndpoint);

    if (configEndpoint == null || configEndpoint.trim().isEmpty()) {
      logger.info("No config endpoint specified, using static configuration");
      System.out.println("[HT-Agent] No config endpoint specified, using static configuration");
      return;
    }

    logger.info("About to load rules from endpoint: {}", configEndpoint);
    System.out.println("[HT-Agent] About to load rules from endpoint: " + configEndpoint);

    // 立即加载一次配置
    loadRulesFromEndpoint();

    // 定期刷新配置
    long intervalSeconds = refreshInterval.toMillis() / 1000;
    scheduler.scheduleWithFixedDelay(
        this::loadRulesFromEndpoint, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);

    logger.info(
        "Started dynamic sanitization rule manager with refresh interval: {}", refreshInterval);

    System.out.println(
        "[HT-Agent] Started dynamic sanitization rule manager with refresh interval: "
            + refreshInterval);
  }

  /** 停止动态规则更新 */
  public void stop() {
    scheduler.shutdown();
    try {
      if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
        scheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      scheduler.shutdownNow();
      Thread.currentThread().interrupt();
    }
  }

  /** 从远程端点加载脱敏规则 */
  private void loadRulesFromEndpoint() {
    HttpURLConnection connection = null;
    try {
      URL url = new URL(configEndpoint);
      connection = (HttpURLConnection) url.openConnection();

      // 设置请求方法和超时
      connection.setRequestMethod("GET");
      connection.setConnectTimeout(connectTimeoutMs);
      connection.setReadTimeout(readTimeoutMs);

      // 设置请求头
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setRequestProperty("Accept", "application/json");

      // 添加认证头
      if (authToken != null && !authToken.trim().isEmpty()) {
        connection.setRequestProperty("Authorization", "Bearer " + authToken);
      }

      // 添加服务名称
      if (serviceName != null) {
        connection.setRequestProperty("X-Service-Name", serviceName);
      }

      // 发送请求并读取响应
      int responseCode = connection.getResponseCode();

      if (responseCode == HttpURLConnection.HTTP_OK) {
        String responseBody = readResponse(connection);

        SanitizationConfig newConfig =
            objectMapper.readValue(responseBody, SanitizationConfig.class);

        // 验证配置
        if (isValidConfig(newConfig)) {
          SanitizationConfig oldConfig = currentConfig.getAndSet(newConfig);
          logger.info(
              "Successfully updated sanitization rules: version={}, rules={}",
              newConfig.getVersion(),
              newConfig.getRules() != null ? newConfig.getRules().size() : 0);

          // 记录配置变化
          logConfigChanges(oldConfig, newConfig);
        } else {
          logger.warn("Received invalid sanitization config, keeping current configuration");
        }
      } else {
        logger.warn("Failed to fetch sanitization config: HTTP {}", responseCode);
      }

    } catch (IOException e) {
      logger.error("Error loading sanitization rules from endpoint: {}", e.getMessage());
      System.out.println(
          "[HT-Agent] Error loading sanitization rules from endpoint: " + e.getMessage());
    } catch (Exception e) {
      logger.error("Unexpected error loading sanitization rules", e);
      System.out.println(
          "[HT-Agent] Unexpected error loading sanitization rules: " + e.getMessage());
    } finally {
      if (connection != null) {
        connection.disconnect();
      }
    }
  }

  /** 读取 HTTP 响应内容 */
  private String readResponse(HttpURLConnection connection) throws IOException {
    StringBuilder response = new StringBuilder();
    try (BufferedReader reader =
        new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
    }
    return response.toString();
  }

  /** 验证配置是否有效 */
  private boolean isValidConfig(SanitizationConfig config) {
    if (config == null) {
      return false;
    }

    // 检查规则是否有效
    if (config.getRules() != null) {
      for (SanitizationRule rule : config.getRules()) {
        if (rule.getId() == null || rule.getType() == null) {
          logger.warn("Invalid rule found: {}", rule);
          return false;
        }
      }
    }

    return true;
  }

  /** 记录配置变化 */
  private void logConfigChanges(SanitizationConfig oldConfig, SanitizationConfig newConfig) {
    if (oldConfig == null) {
      logger.info("Initial sanitization config loaded");
      return;
    }

    if (oldConfig.getVersion() != null && !oldConfig.getVersion().equals(newConfig.getVersion())) {
      logger.info(
          "Config version changed: {} -> {}", oldConfig.getVersion(), newConfig.getVersion());
    }

    int oldRuleCount = oldConfig.getRules() != null ? oldConfig.getRules().size() : 0;
    int newRuleCount = newConfig.getRules() != null ? newConfig.getRules().size() : 0;

    if (oldRuleCount != newRuleCount) {
      logger.info("Rule count changed: {} -> {}", oldRuleCount, newRuleCount);
    }
  }

  /** 获取当前的脱敏配置 */
  public SanitizationConfig getCurrentConfig() {
    return currentConfig.get();
  }

  /** 设置当前的脱敏配置 (主要用于测试) */
  public void setCurrentConfig(SanitizationConfig config) {
    currentConfig.set(config);
  }

  /** 获取当前的脱敏规则列表 */
  public List<SanitizationRule> getCurrentRules() {
    SanitizationConfig config = currentConfig.get();
    return config != null ? config.getRules() : null;
  }

  /** 手动刷新配置 */
  public void refreshConfig() {
    loadRulesFromEndpoint();
  }

  private String getProperty(String name) {
    return getProperty(name, null);
  }

  private String getProperty(String name, String defaultValue) {
    String value = System.getProperty(name);
    if (value == null) {
      value = System.getenv(name.replaceAll("\\.", "_").toUpperCase());
    }
    return value != null ? value : defaultValue;
  }

  /**
   * Get the singleton instance of DynamicSanitizationRuleManager
   *
   * @return the singleton instance, or null if not initialized
   */
  public static DynamicSanitizationRuleManager getInstance() {
    return instance;
  }

  /**
   * Set the singleton instance (should only be called during initialization)
   *
   * @param manager the manager instance to set
   */
  public static void setInstance(DynamicSanitizationRuleManager manager) {
    instance = manager;
  }
}
