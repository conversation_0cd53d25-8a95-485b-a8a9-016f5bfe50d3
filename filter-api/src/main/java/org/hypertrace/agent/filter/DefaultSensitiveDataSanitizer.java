/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Default implementation of SensitiveDataSanitizer that masks common sensitive data patterns */
public class DefaultSensitiveDataSanitizer implements SensitiveDataSanitizer {

  private static final Logger logger = LoggerFactory.getLogger(DefaultSensitiveDataSanitizer.class);
  private static final String MASK = "****";

  // Configuration for adding detection markers
  private final boolean addDetectionMarkers;

  // Marker templates for different sensitive data types
  private static final String FIELD_MARKER_TEMPLATE = "[SENSITIVE:%s]%s";
  private static final String PATTERN_MARKER_TEMPLATE = "[DETECTED:%s]%s";

  // Common sensitive field names (case-insensitive)
  private static final Set<String> SENSITIVE_FIELDS =
      new HashSet<>(
          Arrays.asList(
              "password",
              "passwd",
              "pwd",
              "pass",
              "secret",
              "token",
              "apikey",
              "api_key",
              "api-key",
              "authorization",
              "auth",
              "credit_card",
              "creditcard",
              "card_number",
              "cardnumber",
              "cvv",
              "cvc",
              "cvv2",
              "cvc2",
              "ssn",
              "social_security_number",
              "socialsecuritynumber",
              "email",
              "mail",
              "phone",
              "mobile",
              "telephone",
              "account_number",
              "accountnumber",
              "account",
              "pin",
              "passcode"));

  // Regex patterns for sensitive data
  private static final Pattern EMAIL_PATTERN =
      Pattern.compile("([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})");

  private static final Pattern CREDIT_CARD_PATTERN = Pattern.compile("\\b(?:\\d[ -]*?){13,19}\\b");

  private static final Pattern SSN_PATTERN =
      Pattern.compile("\\b\\d{3}-\\d{2}-\\d{4}\\b|\\b\\d{9}\\b");

  private static final Pattern PHONE_PATTERN =
      Pattern.compile("\\b(?:\\+?1[-.]?)?\\(?\\d{3}\\)?[-.]?\\d{3}[-.]?\\d{4}\\b");

  private static final Pattern API_KEY_PATTERN = Pattern.compile("\\b[A-Za-z0-9]{20,}\\b");

  private final ObjectMapper objectMapper = new ObjectMapper();
  private final Set<String> supportedContentTypes;
  private final boolean enabled;

  public DefaultSensitiveDataSanitizer() {
    this(true, false);
  }

  public DefaultSensitiveDataSanitizer(boolean enabled) {
    this(enabled, false);
  }

  public DefaultSensitiveDataSanitizer(boolean enabled, boolean addDetectionMarkers) {
    this.enabled = enabled;
    this.addDetectionMarkers = addDetectionMarkers;
    this.supportedContentTypes =
        new HashSet<>(
            Arrays.asList(
                "application/json",
                "application/xml",
                "text/xml",
                "application/x-www-form-urlencoded",
                "text/plain"));
  }

  @Override
  public String sanitize(String input, Map<String, String> headers) {
    if (!enabled || input == null || input.isEmpty()) {
      return input;
    }

    String contentType = getContentType(headers);

    try {
      if (isJsonContent(contentType)) {
        return sanitizeJson(input);
      } else if (isXmlContent(contentType)) {
        return sanitizeXml(input);
      } else if (isFormUrlEncoded(contentType)) {
        return sanitizeFormData(input);
      } else {
        // For plain text or unknown content types, apply pattern-based sanitization
        return sanitizeByPatterns(input);
      }
    } catch (Exception e) {
      logger.warn("Error sanitizing data, returning original: {}", e.getMessage());
      return input;
    }
  }

  @Override
  public boolean shouldSanitize(String contentType) {
    if (!enabled) {
      return false;
    }

    if (contentType == null) {
      return true; // Sanitize by default if content type is unknown
    }

    String lowerContentType = contentType.toLowerCase();
    for (String supportedType : supportedContentTypes) {
      if (lowerContentType.contains(supportedType)) {
        return true;
      }
    }
    return false;
  }

  private String getContentType(Map<String, String> headers) {
    if (headers == null) {
      return null;
    }

    // Try different variations of content-type header
    return headers.get("content-type") != null
        ? headers.get("content-type")
        : headers.get("Content-Type") != null
            ? headers.get("Content-Type")
            : headers.get("CONTENT-TYPE");
  }

  private boolean isJsonContent(String contentType) {
    return contentType != null && contentType.toLowerCase().contains("json");
  }

  private boolean isXmlContent(String contentType) {
    return contentType != null
        && (contentType.toLowerCase().contains("xml")
            || contentType.toLowerCase().contains("soap"));
  }

  private boolean isFormUrlEncoded(String contentType) {
    return contentType != null && contentType.toLowerCase().contains("form-urlencoded");
  }

  private String sanitizeJson(String json) throws Exception {
    JsonNode rootNode = objectMapper.readTree(json);
    sanitizeJsonNode(rootNode, "");
    return objectMapper.writeValueAsString(rootNode);
  }

  private void sanitizeJsonNode(JsonNode node, String path) {
    if (node.isObject()) {
      ObjectNode objectNode = (ObjectNode) node;
      Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();

      while (fields.hasNext()) {
        Map.Entry<String, JsonNode> field = fields.next();
        String fieldName = field.getKey();
        JsonNode fieldValue = field.getValue();

        if (isSensitiveField(fieldName) && fieldValue.isValueNode()) {
          // Only mask leaf nodes that are sensitive
          if (fieldValue.isTextual() && fieldName.toLowerCase().contains("email")) {
            // Special handling for email fields - preserve domain
            String email = fieldValue.asText();
            if (email.contains("@")) {
              String domain = email.substring(email.indexOf("@"));
              String maskedValue = MASK + domain;
              if (addDetectionMarkers) {
                maskedValue = String.format(FIELD_MARKER_TEMPLATE, "EMAIL", maskedValue);
              }
              objectNode.put(fieldName, maskedValue);
            } else {
              String maskedValue = MASK;
              if (addDetectionMarkers) {
                maskedValue = String.format(FIELD_MARKER_TEMPLATE, "EMAIL", maskedValue);
              }
              objectNode.put(fieldName, maskedValue);
            }
          } else {
            String maskedValue = MASK;
            if (addDetectionMarkers) {
              String type = getSensitiveFieldType(fieldName);
              maskedValue = String.format(FIELD_MARKER_TEMPLATE, type, maskedValue);
            }
            objectNode.put(fieldName, maskedValue);
          }
        } else if (fieldValue.isTextual()) {
          String sanitized = sanitizeByPatterns(fieldValue.asText());
          if (!sanitized.equals(fieldValue.asText())) {
            objectNode.put(fieldName, sanitized);
          }
        } else if (!fieldValue.isValueNode()) {
          // Recursively sanitize nested objects and arrays
          sanitizeJsonNode(fieldValue, path + "." + fieldName);
        }
      }
    } else if (node.isArray()) {
      for (JsonNode element : node) {
        sanitizeJsonNode(element, path + "[]");
      }
    }
  }

  private String sanitizeXml(String xml) {
    // Simple XML sanitization - can be enhanced with proper XML parsing
    String result = xml;

    // Special handling for email fields
    Pattern emailFieldPattern =
        Pattern.compile("<(email|Email|EMAIL|mail|Mail|MAIL)>([^<]+)</\\1>");
    Matcher emailFieldMatcher = emailFieldPattern.matcher(result);
    StringBuffer emailBuffer = new StringBuffer();
    while (emailFieldMatcher.find()) {
      String tagName = emailFieldMatcher.group(1);
      String emailValue = emailFieldMatcher.group(2);
      if (emailValue.contains("@")) {
        String domain = emailValue.substring(emailValue.indexOf("@"));
        String maskedValue = MASK + domain;
        if (addDetectionMarkers) {
          maskedValue = String.format(FIELD_MARKER_TEMPLATE, "EMAIL", maskedValue);
        }
        emailFieldMatcher.appendReplacement(
            emailBuffer, "<" + tagName + ">" + maskedValue + "</" + tagName + ">");
      } else {
        String maskedValue = MASK;
        if (addDetectionMarkers) {
          maskedValue = String.format(FIELD_MARKER_TEMPLATE, "EMAIL", maskedValue);
        }
        emailFieldMatcher.appendReplacement(
            emailBuffer, "<" + tagName + ">" + maskedValue + "</" + tagName + ">");
      }
    }
    emailFieldMatcher.appendTail(emailBuffer);
    result = emailBuffer.toString();

    // Mask other sensitive XML elements
    for (String field : SENSITIVE_FIELDS) {
      if (!field.equals("email") && !field.equals("mail")) {
        String maskedValue = MASK;
        if (addDetectionMarkers) {
          String type = getSensitiveFieldType(field);
          maskedValue = String.format(FIELD_MARKER_TEMPLATE, type, MASK);
        }

        String pattern = "<" + field + ">([^<]+)</" + field + ">";
        result = result.replaceAll(pattern, "<" + field + ">" + maskedValue + "</" + field + ">");

        // Handle case variations
        String patternUpper = "<" + field.toUpperCase() + ">([^<]+)</" + field.toUpperCase() + ">";
        result =
            result.replaceAll(
                patternUpper,
                "<" + field.toUpperCase() + ">" + maskedValue + "</" + field.toUpperCase() + ">");
      }
    }

    // Apply pattern-based sanitization to text content
    return sanitizeByPatterns(result);
  }

  private String sanitizeFormData(String formData) {
    StringBuilder result = new StringBuilder();
    String[] pairs = formData.split("&");

    for (int i = 0; i < pairs.length; i++) {
      String[] keyValue = pairs[i].split("=", 2);

      if (keyValue.length == 2) {
        String key = keyValue[0];
        String value = keyValue[1];

        if (isSensitiveField(key)) {
          String maskedValue = MASK;
          if (addDetectionMarkers) {
            String type = getSensitiveFieldType(key);
            maskedValue = String.format(FIELD_MARKER_TEMPLATE, type, MASK);
          }
          result.append(key).append("=").append(maskedValue);
        } else {
          String sanitizedValue = sanitizeByPatterns(value);
          result.append(key).append("=").append(sanitizedValue);
        }
      } else {
        result.append(pairs[i]);
      }

      if (i < pairs.length - 1) {
        result.append("&");
      }
    }

    return result.toString();
  }

  private String sanitizeByPatterns(String text) {
    String result = text;

    // Mask email addresses (keep domain for debugging)
    Matcher emailMatcher = EMAIL_PATTERN.matcher(result);
    StringBuffer emailBuffer = new StringBuffer();
    while (emailMatcher.find()) {
      String maskedEmail = MASK + "@" + emailMatcher.group(2);
      if (addDetectionMarkers) {
        maskedEmail = String.format(PATTERN_MARKER_TEMPLATE, "EMAIL", maskedEmail);
      }
      emailMatcher.appendReplacement(emailBuffer, maskedEmail);
    }
    emailMatcher.appendTail(emailBuffer);
    result = emailBuffer.toString();

    // Mask credit card numbers
    if (addDetectionMarkers) {
      result =
          CREDIT_CARD_PATTERN
              .matcher(result)
              .replaceAll(String.format(PATTERN_MARKER_TEMPLATE, "CREDIT_CARD", MASK));
    } else {
      result = CREDIT_CARD_PATTERN.matcher(result).replaceAll(MASK);
    }

    // Mask SSN
    if (addDetectionMarkers) {
      result =
          SSN_PATTERN
              .matcher(result)
              .replaceAll(String.format(PATTERN_MARKER_TEMPLATE, "SSN", MASK));
    } else {
      result = SSN_PATTERN.matcher(result).replaceAll(MASK);
    }

    // Mask phone numbers
    if (addDetectionMarkers) {
      result =
          PHONE_PATTERN
              .matcher(result)
              .replaceAll(String.format(PATTERN_MARKER_TEMPLATE, "PHONE", MASK));
    } else {
      result = PHONE_PATTERN.matcher(result).replaceAll(MASK);
    }

    // Mask potential API keys (long alphanumeric strings)
    Matcher apiKeyMatcher = API_KEY_PATTERN.matcher(result);
    StringBuffer sb = new StringBuffer();
    while (apiKeyMatcher.find()) {
      String match = apiKeyMatcher.group();
      // Only mask if it looks like an API key (mixed case or all caps)
      if (!match.equals(match.toLowerCase()) || match.equals(match.toUpperCase())) {
        String maskedKey = MASK;
        if (addDetectionMarkers) {
          maskedKey = String.format(PATTERN_MARKER_TEMPLATE, "API_KEY", MASK);
        }
        apiKeyMatcher.appendReplacement(sb, maskedKey);
      } else {
        apiKeyMatcher.appendReplacement(sb, match);
      }
    }
    apiKeyMatcher.appendTail(sb);
    result = sb.toString();

    return result;
  }

  private boolean isSensitiveField(String fieldName) {
    if (fieldName == null) {
      return false;
    }

    String lowerFieldName = fieldName.toLowerCase();
    for (String sensitiveField : SENSITIVE_FIELDS) {
      if (lowerFieldName.contains(sensitiveField)) {
        return true;
      }
    }
    return false;
  }

  /** Get the type of sensitive field for marking purposes */
  private String getSensitiveFieldType(String fieldName) {
    if (fieldName == null) {
      return "UNKNOWN";
    }

    String lowerFieldName = fieldName.toLowerCase();

    // Password related
    if (lowerFieldName.contains("password")
        || lowerFieldName.contains("passwd")
        || lowerFieldName.contains("pwd")
        || lowerFieldName.contains("pass")) {
      return "PASSWORD";
    }

    // Token/Key related
    if (lowerFieldName.contains("token")
        || lowerFieldName.contains("secret")
        || lowerFieldName.contains("apikey")
        || lowerFieldName.contains("api_key")
        || lowerFieldName.contains("api-key")
        || lowerFieldName.contains("authorization")
        || lowerFieldName.contains("auth")) {
      return "TOKEN";
    }

    // Payment related
    if (lowerFieldName.contains("credit")
        || lowerFieldName.contains("card")
        || lowerFieldName.contains("cvv")
        || lowerFieldName.contains("cvc")) {
      return "PAYMENT";
    }

    // Personal info
    if (lowerFieldName.contains("ssn") || lowerFieldName.contains("social")) {
      return "SSN";
    }

    if (lowerFieldName.contains("phone")
        || lowerFieldName.contains("mobile")
        || lowerFieldName.contains("telephone")) {
      return "PHONE";
    }

    if (lowerFieldName.contains("email") || lowerFieldName.contains("mail")) {
      return "EMAIL";
    }

    // Account related
    if (lowerFieldName.contains("account")
        || lowerFieldName.contains("pin")
        || lowerFieldName.contains("passcode")) {
      return "ACCOUNT";
    }

    return "SENSITIVE";
  }
}
