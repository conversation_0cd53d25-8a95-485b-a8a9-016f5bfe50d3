/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.utils;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.Span;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.hypertrace.agent.filter.SanitizerRegistry;
import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Utilities for applying data sanitization to captured request/response data */
public class DataSanitizationUtils {

  private static final Logger logger = LoggerFactory.getLogger(DataSanitizationUtils.class);

  private DataSanitizationUtils() {}

  /**
   * Add sanitized body to span attribute
   *
   * @param span the span to add the attribute to
   * @param attributeKey the attribute key
   * @param body the body content to sanitize
   * @param headers request/response headers for context
   */
  public static void addSanitizedAttribute(
      Span span, AttributeKey<String> attributeKey, String body, Map<String, String> headers) {

    if (span == null || body == null || body.isEmpty()) {
      return;
    }

    try {
      SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();
      if (sanitizer != null && shouldSanitize(headers)) {
        String sanitizedBody = sanitizer.sanitize(body, headers);
        span.setAttribute(attributeKey, sanitizedBody);
      } else {
        span.setAttribute(attributeKey, body);
      }
    } catch (Exception e) {
      logger.warn("Failed to sanitize body data", e);
      // In case of error, don't add the attribute to avoid exposing sensitive data
    }
  }

  /**
   * Sanitize body content
   *
   * @param body the body content to sanitize
   * @param headers request/response headers for context
   * @return sanitized body or original if sanitization is disabled/fails
   */
  public static String sanitize(String body, Map<String, String> headers) {
    if (body == null || body.isEmpty()) {
      return body;
    }

    try {
      SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();
      if (sanitizer != null && shouldSanitize(headers)) {
        return sanitizer.sanitize(body, headers);
      }
    } catch (Exception e) {
      logger.warn("Failed to sanitize body data", e);
    }

    return body;
  }

  /**
   * Sanitize body content (simplified version without headers)
   *
   * @param body the body content to sanitize
   * @return sanitized body or original if sanitization is disabled/fails
   */
  public static String sanitize(String body) {
    return sanitize(body, null);
  }

  /**
   * Sanitize byte array body content
   *
   * @param body the body content to sanitize as byte array
   * @param charset the charset to use for converting bytes to string
   * @return sanitized body as string
   */
  public static String sanitize(byte[] body, Charset charset) {
    if (body == null || body.length == 0) {
      return "";
    }

    if (charset == null) {
      charset = StandardCharsets.UTF_8;
    }

    String bodyString = new String(body, charset);
    return sanitize(bodyString);
  }

  /**
   * Sanitize byte array body content with headers
   *
   * @param body the body content to sanitize as byte array
   * @param charset the charset to use for converting bytes to string
   * @param headers request/response headers for context
   * @return sanitized body as string
   */
  public static String sanitize(byte[] body, Charset charset, Map<String, String> headers) {
    if (body == null || body.length == 0) {
      return "";
    }

    if (charset == null) {
      charset = StandardCharsets.UTF_8;
    }

    String bodyString = new String(body, charset);
    return sanitize(bodyString, headers);
  }

  /**
   * Check if sanitization should be applied based on headers
   *
   * @param headers request/response headers
   * @return true if sanitization should be applied
   */
  private static boolean shouldSanitize(Map<String, String> headers) {
    if (headers == null) {
      return true; // Sanitize by default if no headers
    }

    String contentType = headers.get("content-type");
    if (contentType == null) {
      contentType = headers.get("Content-Type");
    }

    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();
    return sanitizer != null && sanitizer.shouldSanitize(contentType);
  }
}
