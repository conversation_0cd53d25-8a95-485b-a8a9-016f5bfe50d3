/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import com.google.auto.service.AutoService;
import io.opentelemetry.api.trace.Span;
import java.util.Map;
import org.hypertrace.agent.core.filter.FilterResult;
import org.hypertrace.agent.filter.api.Filter;
import org.hypertrace.agent.filter.spi.FilterProvider;
import org.hypertrace.agent.filter.spi.FilterProviderConfig;

/** FilterProvider that provides a filter which sanitizes sensitive data in request bodies */
@AutoService(FilterProvider.class)
public class SanitizingFilterProvider implements FilterProvider {

  @Override
  public Filter create(FilterProviderConfig config) {
    if (!SanitizerRegistry.isSanitizationEnabled()) {
      // Return a no-op filter if sanitization is disabled
      return new NoOpFilter();
    }

    return new DataSanitizingFilter();
  }

  /** Filter that sanitizes sensitive data in request bodies before evaluation */
  private static class DataSanitizingFilter implements Filter {

    @Override
    public FilterResult evaluateRequestHeaders(Span span, Map<String, String> headers) {
      // Headers are not sanitized in this implementation
      return new FilterResult(false, 0, null);
    }

    @Override
    public FilterResult evaluateRequestBody(Span span, String body, Map<String, String> headers) {
      // We don't actually filter/block requests based on sanitized content
      // This filter's purpose is to sanitize data that gets added to spans
      // The actual sanitization happens in instrumentation code that calls SanitizerRegistry
      return new FilterResult(false, 0, null);
    }
  }

  /** No-op filter for when sanitization is disabled */
  private static class NoOpFilter implements Filter {

    @Override
    public FilterResult evaluateRequestHeaders(Span span, Map<String, String> headers) {
      return new FilterResult(false, 0, null);
    }

    @Override
    public FilterResult evaluateRequestBody(Span span, String body, Map<String, String> headers) {
      return new FilterResult(false, 0, null);
    }
  }
}
