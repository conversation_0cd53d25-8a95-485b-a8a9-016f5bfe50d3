/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.config;

import java.util.List;
import java.util.Map;

/** Configuration container for sanitization rules */
public class SanitizationConfig {

  private String version; // 配置版本号
  private long timestamp; // 配置时间戳
  private boolean enabled; // 全局启用/禁用
  private boolean markersEnabled; // 是否启用标记
  private String markerFormat; // 标记格式
  private List<SanitizationRule> rules; // 脱敏规则列表
  private Map<String, Object> globalSettings; // 全局设置

  // 默认构造函数
  public SanitizationConfig() {
    this.enabled = true;
    this.markersEnabled = false;
    this.markerFormat = "BRACKET";
    this.timestamp = System.currentTimeMillis();
  }

  // Getters and Setters
  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(long timestamp) {
    this.timestamp = timestamp;
  }

  public boolean isEnabled() {
    return enabled;
  }

  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }

  public boolean isMarkersEnabled() {
    return markersEnabled;
  }

  public void setMarkersEnabled(boolean markersEnabled) {
    this.markersEnabled = markersEnabled;
  }

  public String getMarkerFormat() {
    return markerFormat;
  }

  public void setMarkerFormat(String markerFormat) {
    this.markerFormat = markerFormat;
  }

  public List<SanitizationRule> getRules() {
    return rules;
  }

  public void setRules(List<SanitizationRule> rules) {
    this.rules = rules;
  }

  public Map<String, Object> getGlobalSettings() {
    return globalSettings;
  }

  public void setGlobalSettings(Map<String, Object> globalSettings) {
    this.globalSettings = globalSettings;
  }

  @Override
  public String toString() {
    return "SanitizationConfig{"
        + "version='"
        + version
        + '\''
        + ", timestamp="
        + timestamp
        + ", enabled="
        + enabled
        + ", markersEnabled="
        + markersEnabled
        + ", rulesCount="
        + (rules != null ? rules.size() : 0)
        + '}';
  }
}
