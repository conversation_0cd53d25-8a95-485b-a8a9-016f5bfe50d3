{"version": "1.0.0", "timestamp": 1703123456789, "enabled": true, "markersEnabled": true, "markerFormat": "BRACKET", "globalSettings": {"defaultMaskValue": "****", "maxRuleCount": 100, "cachePatterns": true}, "rules": [{"id": "password-fields", "name": "Password Field Detection", "description": "Detect and sanitize password-related fields", "type": "FIELD_NAME", "severity": "HIGH", "enabled": true, "priority": 10, "fieldNames": ["password", "passwd", "pwd", "pass", "secret", "passphrase"], "maskValue": "****", "markerType": "PASSWORD", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "application/x-www-form-urlencoded"]}, {"id": "email-fields", "name": "Email Field Detection", "description": "Detect and sanitize email fields while preserving domain", "type": "FIELD_NAME", "severity": "MEDIUM", "enabled": true, "priority": 20, "fieldNames": ["email", "mail", "emailAddress", "userEmail"], "maskValue": "****", "markerType": "EMAIL", "preserveFormat": true, "contentTypes": ["application/json", "application/xml", "application/x-www-form-urlencoded"]}, {"id": "credit-card-pattern", "name": "Credit Card Pattern Detection", "description": "Detect credit card numbers using regex pattern", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 5, "pattern": "\\b(?:\\d[ -]*?){13,19}\\b", "maskValue": "****", "markerType": "CREDIT_CARD", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "text/plain", "application/x-www-form-urlencoded"]}, {"id": "ssn-pattern", "name": "Social Security Number Pattern", "description": "Detect SSN in XXX-XX-XXXX format", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 5, "pattern": "\\b\\d{3}-\\d{2}-\\d{4}\\b", "maskValue": "***-**-****", "markerType": "SSN", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "text/plain", "application/x-www-form-urlencoded"]}, {"id": "phone-pattern", "name": "Phone Number Pattern", "description": "Detect US phone numbers", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 15, "pattern": "\\b(?:\\+?1[-.]?)?\\(?\\d{3}\\)?[-.]?\\d{3}[-.]?\\d{4}\\b", "maskValue": "***-***-****", "markerType": "PHONE", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "text/plain", "application/x-www-form-urlencoded"]}, {"id": "api-key-fields", "name": "API Key Fields", "description": "Detect API key and token fields", "type": "FIELD_NAME", "severity": "CRITICAL", "enabled": true, "priority": 1, "fieldNames": ["<PERSON><PERSON><PERSON><PERSON>", "api_key", "api-key", "token", "accessToken", "access_token", "authToken", "auth_token", "bearerToken", "bearer_token", "secret<PERSON>ey", "secret_key"], "maskValue": "****", "markerType": "TOKEN", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "application/x-www-form-urlencoded"]}, {"id": "payment-fields", "name": "Payment Information Fields", "description": "Detect payment-related sensitive fields", "type": "FIELD_NAME", "severity": "HIGH", "enabled": true, "priority": 5, "fieldNames": ["creditCard", "credit_card", "cardNumber", "card_number", "cvv", "cvc", "cvv2", "cvc2", "expiryDate", "expiry_date", "bankAccount", "bank_account", "routingNumber", "routing_number"], "maskValue": "****", "markerType": "PAYMENT", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "application/x-www-form-urlencoded"]}, {"id": "personal-info-fields", "name": "Personal Information Fields", "description": "Detect personal information fields", "type": "FIELD_NAME", "severity": "MEDIUM", "enabled": true, "priority": 25, "fieldNames": ["ssn", "social_security_number", "socialSecurityNumber", "driverLicense", "driver_license", "passport", "passportNumber", "passport_number", "nationalId", "national_id", "taxId", "tax_id"], "maskValue": "****", "markerType": "PERSONAL_ID", "preserveFormat": false, "contentTypes": ["application/json", "application/xml", "application/x-www-form-urlencoded"]}, {"id": "custom-internal-id", "name": "Custom Internal Employee ID", "description": "Company-specific employee ID pattern", "type": "PATTERN", "severity": "LOW", "enabled": true, "priority": 30, "pattern": "\\bemp\\d{6}\\b", "maskValue": "emp******", "markerType": "INTERNAL_ID", "preserveFormat": false, "includeServices": ["hr-service", "payroll-service"], "contentTypes": ["application/json", "text/plain"]}]}