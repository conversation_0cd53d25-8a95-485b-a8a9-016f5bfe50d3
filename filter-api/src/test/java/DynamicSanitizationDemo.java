/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.DynamicSensitiveDataSanitizer;
import org.hypertrace.agent.filter.config.SanitizationConfig;

/** Demo program to showcase dynamic sanitization rules */
public class DynamicSanitizationDemo {

  public static void main(String[] args) {
    System.out.println("=== 动态脱敏规则演示 ===\n");

    try {
      // 模拟从配置文件加载规则
      demonstrateConfigLoading();

      // 演示不同类型的脱敏规则
      demonstrateSanitization();

    } catch (Exception e) {
      System.err.println("演示过程中发生错误: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /** 演示配置加载 */
  private static void demonstrateConfigLoading() throws IOException {
    System.out.println("1. 配置加载演示");
    System.out.println("================");

    // 读取示例配置文件
    String configPath = "src/test/resources/sanitization-config-example.json";
    String configJson =
        new String(Files.readAllBytes(Paths.get(configPath)), StandardCharsets.UTF_8);

    ObjectMapper mapper = new ObjectMapper();
    SanitizationConfig config = mapper.readValue(configJson, SanitizationConfig.class);

    System.out.println("配置版本: " + config.getVersion());
    System.out.println("全局启用: " + config.isEnabled());
    System.out.println("标记启用: " + config.isMarkersEnabled());
    System.out.println("规则数量: " + (config.getRules() != null ? config.getRules().size() : 0));

    if (config.getRules() != null) {
      System.out.println("\n规则列表:");
      config
          .getRules()
          .forEach(
              rule -> {
                System.out.printf(
                    "  - %s (%s, 优先级: %d, 严重性: %s)%n",
                    rule.getName(), rule.getType(), rule.getPriority(), rule.getSeverity());
              });
    }

    System.out.println("\n" + String.join("", java.util.Collections.nCopies(50, "=")) + "\n");
  }

  /** 演示脱敏功能 */
  private static void demonstrateSanitization() {
    System.out.println("2. 动态脱敏演示");
    System.out.println("================");

    // 注意：这里为了演示目的，我们直接使用默认的脱敏器
    // 在实际应用中，动态脱敏器会从远程端点加载规则
    DynamicSensitiveDataSanitizer sanitizer = new DynamicSensitiveDataSanitizer();

    // 测试 JSON 数据
    testJsonSanitization(sanitizer);

    // 测试 XML 数据
    testXmlSanitization(sanitizer);

    // 测试表单数据
    testFormSanitization(sanitizer);

    // 测试纯文本数据
    testTextSanitization(sanitizer);

    // 关闭资源
    sanitizer.shutdown();
  }

  private static void testJsonSanitization(DynamicSensitiveDataSanitizer sanitizer) {
    System.out.println("JSON 数据脱敏测试:");
    System.out.println("------------------");

    String json =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"password\":\"mySecretPassword123\","
            + "\"creditCard\":\"4111-1111-1111-1111\","
            + "\"apiKey\":\"sk_live_abcdef123456789012345678\","
            + "\"ssn\":\"***********\","
            + "\"phone\":\"+1-************\""
            + "},"
            + "\"metadata\":{"
            + "\"employeeId\":\"emp123456\","
            + "\"department\":\"Engineering\""
            + "}"
            + "}";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    System.out.println("原始数据:");
    System.out.println(json);
    System.out.println();

    String sanitized = sanitizer.sanitize(json, headers);
    System.out.println("脱敏后数据:");
    System.out.println(sanitized);
    System.out.println();
  }

  private static void testXmlSanitization(DynamicSensitiveDataSanitizer sanitizer) {
    System.out.println("XML 数据脱敏测试:");
    System.out.println("------------------");

    String xml =
        "<user>"
            + "<email><EMAIL></email>"
            + "<password>xmlPassword789</password>"
            + "<creditCard>5555-5555-5555-4444</creditCard>"
            + "<ssn>***********</ssn>"
            + "<phone>************</phone>"
            + "<department>Marketing</department>"
            + "</user>";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/xml");

    System.out.println("原始数据:");
    System.out.println(xml);
    System.out.println();

    String sanitized = sanitizer.sanitize(xml, headers);
    System.out.println("脱敏后数据:");
    System.out.println(sanitized);
    System.out.println();
  }

  private static void testFormSanitization(DynamicSensitiveDataSanitizer sanitizer) {
    System.out.println("表单数据脱敏测试:");
    System.out.println("------------------");

    String formData =
        "email=<EMAIL>&password=formPassword123&"
            + "creditCard=4000-0000-0000-0002&token=bearer_xyz789&"
            + "ssn=***********&phone=************&department=IT";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/x-www-form-urlencoded");

    System.out.println("原始数据:");
    System.out.println(formData);
    System.out.println();

    String sanitized = sanitizer.sanitize(formData, headers);
    System.out.println("脱敏后数据:");
    System.out.println(sanitized);
    System.out.println();
  }

  private static void testTextSanitization(DynamicSensitiveDataSanitizer sanitizer) {
    System.out.println("纯文本数据脱敏测试:");
    System.out.println("------------------");

    String text =
        "联系信息: 邮箱 <EMAIL>, 电话 ************, "
            + "社保号 ***********, 信用卡 4111-1111-1111-1111, "
            + "员工ID emp123456, API密钥 sk_test_1234567890abcdefghijklmnop";

    System.out.println("原始数据:");
    System.out.println(text);
    System.out.println();

    String sanitized = sanitizer.sanitize(text, null);
    System.out.println("脱敏后数据:");
    System.out.println(sanitized);
    System.out.println();
  }
}
