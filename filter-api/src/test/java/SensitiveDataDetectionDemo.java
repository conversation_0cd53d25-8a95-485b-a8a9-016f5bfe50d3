/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.DefaultSensitiveDataSanitizer;

/** Demo program to showcase sensitive data detection with markers */
public class SensitiveDataDetectionDemo {

  public static void main(String[] args) {
    System.out.println("=== 敏感数据检测演示 ===\n");

    // Create sanitizers with and without markers
    DefaultSensitiveDataSanitizer sanitizerWithoutMarkers =
        new DefaultSensitiveDataSanitizer(true, false);
    DefaultSensitiveDataSanitizer sanitizerWithMarkers =
        new DefaultSensitiveDataSanitizer(true, true);

    // Test JSON data
    testJsonData(sanitizerWithoutMarkers, sanitizerWithMarkers);

    // Test XML data
    testXmlData(sanitizerWithoutMarkers, sanitizerWithMarkers);

    // Test Form data
    testFormData(sanitizerWithoutMarkers, sanitizerWithMarkers);

    // Test pattern detection
    testPatternDetection(sanitizerWithoutMarkers, sanitizerWithMarkers);
  }

  private static void testJsonData(
      DefaultSensitiveDataSanitizer without, DefaultSensitiveDataSanitizer with) {
    System.out.println("1. JSON 数据测试");
    System.out.println("================");

    String json =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"password\":\"mySecretPassword123\","
            + "\"creditCard\":\"****************\","
            + "\"apiKey\":\"sk_live_abcdef123456789012345678\","
            + "\"ssn\":\"***********\""
            + "},"
            + "\"data\":\"regular data\""
            + "}";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    System.out.println("原始数据:");
    System.out.println(json);
    System.out.println();

    System.out.println("不带标记的脱敏结果:");
    String resultWithout = without.sanitize(json, headers);
    System.out.println(resultWithout);
    System.out.println();

    System.out.println("带标记的脱敏结果:");
    String resultWith = with.sanitize(json, headers);
    System.out.println(resultWith);
    System.out.println("\n" + String.join("", java.util.Collections.nCopies(50, "=")) + "\n");
  }

  private static void testXmlData(
      DefaultSensitiveDataSanitizer without, DefaultSensitiveDataSanitizer with) {
    System.out.println("2. XML 数据测试");
    System.out.println("===============");

    String xml =
        "<user>"
            + "<email><EMAIL></email>"
            + "<password>anotherSecret456</password>"
            + "<creditCard>****************</creditCard>"
            + "<phone>+1-************</phone>"
            + "</user>";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/xml");

    System.out.println("原始数据:");
    System.out.println(xml);
    System.out.println();

    System.out.println("不带标记的脱敏结果:");
    String resultWithout = without.sanitize(xml, headers);
    System.out.println(resultWithout);
    System.out.println();

    System.out.println("带标记的脱敏结果:");
    String resultWith = with.sanitize(xml, headers);
    System.out.println(resultWith);
    System.out.println("\n" + String.join("", java.util.Collections.nCopies(50, "=")) + "\n");
  }

  private static void testFormData(
      DefaultSensitiveDataSanitizer without, DefaultSensitiveDataSanitizer with) {
    System.out.println("3. Form 数据测试");
    System.out.println("================");

    String formData =
        "username=<EMAIL>&password=formPassword789&"
            + "creditCard=****************&token=bearer_abc123xyz&pin=1234";

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/x-www-form-urlencoded");

    System.out.println("原始数据:");
    System.out.println(formData);
    System.out.println();

    System.out.println("不带标记的脱敏结果:");
    String resultWithout = without.sanitize(formData, headers);
    System.out.println(resultWithout);
    System.out.println();

    System.out.println("带标记的脱敏结果:");
    String resultWith = with.sanitize(formData, headers);
    System.out.println(resultWith);
    System.out.println("\n" + String.join("", java.util.Collections.nCopies(50, "=")) + "\n");
  }

  private static void testPatternDetection(
      DefaultSensitiveDataSanitizer without, DefaultSensitiveDataSanitizer with) {
    System.out.println("4. 模式检测测试");
    System.out.println("===============");

    String text =
        "Contact info: email <EMAIL>, phone ************, "
            + "SSN ***********, credit card 4111-1111-1111-1111, "
            + "API key sk_test_1234567890abcdefghijklmnop";

    System.out.println("原始数据:");
    System.out.println(text);
    System.out.println();

    System.out.println("不带标记的脱敏结果:");
    String resultWithout = without.sanitize(text, null);
    System.out.println(resultWithout);
    System.out.println();

    System.out.println("带标记的脱敏结果:");
    String resultWith = with.sanitize(text, null);
    System.out.println(resultWith);
    System.out.println("\n" + String.join("", java.util.Collections.nCopies(50, "=")) + "\n");
  }
}
