/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.DefaultSensitiveDataSanitizer;

public class TestSanitizer {
  public static void main(String[] args) {
    DefaultSensitiveDataSanitizer sanitizer = new DefaultSensitiveDataSanitizer(true);
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    String input =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"auth\":{"
            + "\"token\":\"Bearer abc123xyz\","
            + "\"secret\":\"supersecret\""
            + "}"
            + "},"
            + "\"data\":\"regular data\""
            + "}";

    System.out.println("Input: " + input);
    String result = sanitizer.sanitize(input, headers);
    System.out.println("Result: " + result);

    // Check
    System.out.println("\nChecking...");
    System.out.println("Contains email: " + result.contains("\"email\":\"****@example.com\""));
    System.out.println("Contains token: " + result.contains("\"token\":\"****\""));
    System.out.println("Contains secret: " + result.contains("\"secret\":\"****\""));
  }
}
