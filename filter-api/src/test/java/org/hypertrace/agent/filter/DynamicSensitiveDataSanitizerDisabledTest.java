/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;
import org.hypertrace.agent.filter.config.SanitizationConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test to verify that DynamicSensitiveDataSanitizer correctly handles disabled sanitization.
 * When config.isEnabled() returns false, sanitization should be completely disabled,
 * not falling back to default sanitizer.
 */
class DynamicSensitiveDataSanitizerDisabledTest {

  private DynamicSensitiveDataSanitizer sanitizer;

  @BeforeEach
  void setUp() {
    // Clear any existing singleton instance
    DynamicSanitizationRuleManager.setInstance(null);
    
    // Create sanitizer
    sanitizer = new DynamicSensitiveDataSanitizer();
  }

  @AfterEach
  void tearDown() {
    // Clear singleton instance
    DynamicSanitizationRuleManager.setInstance(null);
  }

  @Test
  void testSanitizeWhenDisabled_ReturnsOriginalInput() {
    // Create a config with sanitization disabled
    SanitizationConfig disabledConfig = new SanitizationConfig();
    disabledConfig.setEnabled(false);
    
    // Create and set up rule manager with disabled config
    DynamicSanitizationRuleManager ruleManager = new DynamicSanitizationRuleManager();
    ruleManager.setCurrentConfig(disabledConfig);
    DynamicSanitizationRuleManager.setInstance(ruleManager);
    
    // Test data with sensitive information
    String originalInput = "{\"username\":\"john\",\"password\":\"secret123\",\"email\":\"<EMAIL>\"}";
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
    
    // Sanitize the input
    String result = sanitizer.sanitize(originalInput, headers);
    
    // Should return original input unchanged when disabled
    assertEquals(originalInput, result, "When sanitization is disabled, original input should be returned unchanged");
  }

  @Test
  void testShouldSanitizeWhenDisabled_ReturnsFalse() {
    // Create a config with sanitization disabled
    SanitizationConfig disabledConfig = new SanitizationConfig();
    disabledConfig.setEnabled(false);
    
    // Create and set up rule manager with disabled config
    DynamicSanitizationRuleManager ruleManager = new DynamicSanitizationRuleManager();
    ruleManager.setCurrentConfig(disabledConfig);
    DynamicSanitizationRuleManager.setInstance(ruleManager);
    
    // Test different content types
    assertFalse(sanitizer.shouldSanitize("application/json"), 
        "Should not sanitize JSON when disabled");
    assertFalse(sanitizer.shouldSanitize("application/xml"), 
        "Should not sanitize XML when disabled");
    assertFalse(sanitizer.shouldSanitize("application/x-www-form-urlencoded"), 
        "Should not sanitize form data when disabled");
    assertFalse(sanitizer.shouldSanitize(null), 
        "Should not sanitize unknown content type when disabled");
  }

  @Test
  void testSanitizeWhenEnabled_ProcessesInput() {
    // Create a config with sanitization enabled
    SanitizationConfig enabledConfig = new SanitizationConfig();
    enabledConfig.setEnabled(true);
    
    // Create and set up rule manager with enabled config
    DynamicSanitizationRuleManager ruleManager = new DynamicSanitizationRuleManager();
    ruleManager.setCurrentConfig(enabledConfig);
    DynamicSanitizationRuleManager.setInstance(ruleManager);
    
    // Test data with sensitive information
    String originalInput = "{\"username\":\"john\",\"password\":\"secret123\"}";
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
    
    // Sanitize the input
    String result = sanitizer.sanitize(originalInput, headers);
    
    // Should process the input (either with dynamic rules or fallback to default sanitizer)
    assertNotNull(result, "Result should not be null when enabled");
    // Note: The exact result depends on whether there are rules or it falls back to default sanitizer
  }

  @Test
  void testSanitizeWhenNoConfig_UsesDefaultSanitizer() {
    // Don't set up any rule manager (config will be null)
    
    // Test data with sensitive information
    String originalInput = "{\"username\":\"john\",\"password\":\"secret123\"}";
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
    
    // Sanitize the input
    String result = sanitizer.sanitize(originalInput, headers);
    
    // Should fall back to default sanitizer behavior
    assertNotNull(result, "Result should not be null when no config");
    assertNotEquals(originalInput, result, "Should be processed by default sanitizer");
    assertTrue(result.contains("****"), "Should contain masked values from default sanitizer");
  }

  @Test
  void testShouldSanitizeWhenNoConfig_UsesDefaultSanitizer() {
    // Don't set up any rule manager (config will be null)
    
    // Should fall back to default sanitizer logic
    assertTrue(sanitizer.shouldSanitize("application/json"), 
        "Should use default sanitizer logic for JSON when no config");
    assertTrue(sanitizer.shouldSanitize("application/xml"), 
        "Should use default sanitizer logic for XML when no config");
  }

  @Test
  void testDisabledVsNoRules_DifferentBehavior() {
    // Test 1: Disabled config
    SanitizationConfig disabledConfig = new SanitizationConfig();
    disabledConfig.setEnabled(false);
    
    DynamicSanitizationRuleManager ruleManager1 = new DynamicSanitizationRuleManager();
    ruleManager1.setCurrentConfig(disabledConfig);
    DynamicSanitizationRuleManager.setInstance(ruleManager1);
    
    String input = "{\"password\":\"secret\"}";
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
    
    String disabledResult = sanitizer.sanitize(input, headers);
    
    // Test 2: Enabled config but no rules
    SanitizationConfig enabledConfigNoRules = new SanitizationConfig();
    enabledConfigNoRules.setEnabled(true);
    enabledConfigNoRules.setRules(null); // No rules
    
    DynamicSanitizationRuleManager ruleManager2 = new DynamicSanitizationRuleManager();
    ruleManager2.setCurrentConfig(enabledConfigNoRules);
    DynamicSanitizationRuleManager.setInstance(ruleManager2);
    
    String noRulesResult = sanitizer.sanitize(input, headers);
    
    // Verify different behaviors
    assertEquals(input, disabledResult, "Disabled config should return original input");
    assertNotEquals(input, noRulesResult, "Enabled config with no rules should use default sanitizer");
    assertNotEquals(disabledResult, noRulesResult, "Disabled and no-rules should behave differently");
  }
}
