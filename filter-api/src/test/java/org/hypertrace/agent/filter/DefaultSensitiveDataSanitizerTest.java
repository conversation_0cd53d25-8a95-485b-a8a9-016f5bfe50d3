/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DefaultSensitiveDataSanitizerTest {

  private DefaultSensitiveDataSanitizer sanitizer;
  private Map<String, String> jsonHeaders;
  private Map<String, String> xmlHeaders;
  private Map<String, String> formHeaders;

  @BeforeEach
  void setUp() {
    sanitizer = new DefaultSensitiveDataSanitizer(true);

    jsonHeaders = new HashMap<>();
    jsonHeaders.put("Content-Type", "application/json");

    xmlHeaders = new HashMap<>();
    xmlHeaders.put("Content-Type", "application/xml");

    formHeaders = new HashMap<>();
    formHeaders.put("Content-Type", "application/x-www-form-urlencoded");
  }

  @Test
  void testSanitizeJson_SensitiveFields() {
    String input =
        "{"
            + "\"username\":\"<EMAIL>\","
            + "\"password\":\"secretPassword123\","
            + "\"api_key\":\"sk_test_AbCdEfGhIjKlMnOpQrStUvWx\","
            + "\"credit_card\":\"4111-1111-1111-1111\","
            + "\"ssn\":\"***********\","
            + "\"phone\":\"************\""
            + "}";

    String result = sanitizer.sanitize(input, jsonHeaders);

    assertNotNull(result);
    assertTrue(result.contains("\"username\":\"****@example.com\""));
    assertTrue(result.contains("\"password\":\"****\""));
    assertTrue(result.contains("\"api_key\":\"****\""));
    assertTrue(result.contains("\"credit_card\":\"****\""));
    assertTrue(result.contains("\"ssn\":\"****\""));
    assertTrue(result.contains("\"phone\":\"****\""));
  }

  @Test
  void testSanitizeJson_NestedObjects() {
    String input =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"auth\":{"
            + "\"token\":\"Bearer abc123xyz\","
            + "\"secret\":\"supersecret\""
            + "}"
            + "},"
            + "\"data\":\"regular data\""
            + "}";

    String result = sanitizer.sanitize(input, jsonHeaders);

    assertNotNull(result);
    if (!result.contains("\"token\":\"****\"")) {
      System.out.println("testSanitizeJson_NestedObjects failed. Result: " + result);
    }
    assertTrue(result.contains("\"email\":\"****@example.com\""));
    assertTrue(result.contains("\"token\":\"****\""));
    assertTrue(result.contains("\"secret\":\"****\""));
    assertTrue(result.contains("\"data\":\"regular data\"")); // Non-sensitive data preserved
  }

  @Test
  void testSanitizeXml() {
    String input =
        "<user>"
            + "<email><EMAIL></email>"
            + "<password>secret123</password>"
            + "<account_number>**********</account_number>"
            + "<name>John Doe</name>"
            + "</user>";

    String result = sanitizer.sanitize(input, xmlHeaders);

    assertNotNull(result);
    assertTrue(result.contains("<email>****@example.com</email>"));
    assertTrue(result.contains("<password>****</password>"));
    assertTrue(result.contains("<account_number>****</account_number>"));
    assertTrue(result.contains("<name>John Doe</name>")); // Non-sensitive data preserved
  }

  @Test
  void testSanitizeFormData() {
    String input =
        "username=<EMAIL>&password=secret123&"
            + "token=abc123xyz&name=John+Doe&credit_card=****************";

    String result = sanitizer.sanitize(input, formHeaders);

    assertNotNull(result);
    assertTrue(result.contains("username=****@example.com"));
    assertTrue(result.contains("password=****"));
    assertTrue(result.contains("token=****"));
    assertTrue(result.contains("name=John+Doe")); // Non-sensitive data preserved
    assertTrue(result.contains("credit_card=****"));
  }

  @Test
  void testSanitizePlainText() {
    Map<String, String> plainHeaders = new HashMap<>();
    plainHeaders.put("Content-Type", "text/plain");

    String input =
        "My <NAME_EMAIL> and my phone is ************. "
            + "My SSN is *********** and credit card is 4111-1111-1111-1111.";

    String result = sanitizer.sanitize(input, plainHeaders);

    assertNotNull(result);
    assertTrue(result.contains("****@example.com"));
    assertTrue(result.contains("My SSN is ****"));
    assertTrue(result.contains("credit card is ****"));
    assertTrue(result.contains("phone is ****"));
  }

  @Test
  void testDisabledSanitizer() {
    DefaultSensitiveDataSanitizer disabledSanitizer = new DefaultSensitiveDataSanitizer(false);

    String input = "{\"password\":\"secret123\",\"email\":\"<EMAIL>\"}";
    String result = disabledSanitizer.sanitize(input, jsonHeaders);

    assertEquals(input, result); // Should return original when disabled
  }

  @Test
  void testNullAndEmptyInput() {
    assertNull(sanitizer.sanitize(null, jsonHeaders));
    assertEquals("", sanitizer.sanitize("", jsonHeaders));
  }

  @Test
  void testInvalidJson() {
    String invalidJson = "{\"password\":\"secret123\","; // Missing closing brace
    String result = sanitizer.sanitize(invalidJson, jsonHeaders);

    // Should return original on parse error
    assertEquals(invalidJson, result);
  }

  @Test
  void testShouldSanitize() {
    assertTrue(sanitizer.shouldSanitize("application/json"));
    assertTrue(sanitizer.shouldSanitize("application/xml"));
    assertTrue(sanitizer.shouldSanitize("text/xml"));
    assertTrue(sanitizer.shouldSanitize("application/x-www-form-urlencoded"));
    assertTrue(sanitizer.shouldSanitize("text/plain"));
    assertTrue(sanitizer.shouldSanitize(null)); // Default to true for unknown

    assertFalse(sanitizer.shouldSanitize("image/jpeg"));
    assertFalse(sanitizer.shouldSanitize("application/octet-stream"));
  }

  @Test
  void testCaseInsensitiveFieldNames() {
    String input =
        "{"
            + "\"PASSWORD\":\"secret123\","
            + "\"Email\":\"<EMAIL>\","
            + "\"CREDIT_CARD\":\"****************\""
            + "}";

    String result = sanitizer.sanitize(input, jsonHeaders);

    assertTrue(result.contains("\"PASSWORD\":\"****\""));
    assertTrue(result.contains("\"Email\":\"****@example.com\""));
    assertTrue(result.contains("\"CREDIT_CARD\":\"****\""));
  }

  @Test
  void testPartialFieldNameMatching() {
    String input =
        "{"
            + "\"user_password\":\"secret\","
            + "\"password_confirmation\":\"secret\","
            + "\"old_password\":\"oldsecret\","
            + "\"apikey_name\":\"my-key\","
            + "\"email_address\":\"<EMAIL>\""
            + "}";

    String result = sanitizer.sanitize(input, jsonHeaders);

    // All fields containing sensitive keywords should be masked
    assertTrue(result.contains("\"user_password\":\"****\""));
    assertTrue(result.contains("\"password_confirmation\":\"****\""));
    assertTrue(result.contains("\"old_password\":\"****\""));
    assertTrue(result.contains("\"apikey_name\":\"****\""));
    assertTrue(result.contains("\"email_address\":\"****@example.com\""));
  }

  @Test
  void testApiKeyPatternMatching() {
    String input =
        "{"
            + "\"data1\":\"ABCDEFGHIJKLMNOPQRST\","
            + // 20 chars, all caps - should mask
            "\"data2\":\"AbCdEfGhIjKlMnOpQrSt\","
            + // 20 chars, mixed case - should mask
            "\"data3\":\"abcdefghijklmnopqrst\","
            + // 20 chars, all lowercase - should NOT mask
            "\"data4\":\"ABC123DEF456GHI789JK\""
            + // 20 chars, alphanumeric - should mask
            "}";

    String result = sanitizer.sanitize(input, jsonHeaders);

    assertTrue(result.contains("\"data1\":\"****\""));
    assertTrue(result.contains("\"data2\":\"****\""));
    assertTrue(result.contains("\"data3\":\"abcdefghijklmnopqrst\"")); // Not masked
    assertTrue(result.contains("\"data4\":\"****\""));
  }

  @Test
  void testXmlCaseVariations() {
    String input =
        "<root>"
            + "<PASSWORD>secret</PASSWORD>"
            + "<password>secret2</password>"
            + "<Password>secret3</Password>"
            + "</root>";

    String result = sanitizer.sanitize(input, xmlHeaders);

    assertTrue(result.contains("<PASSWORD>****</PASSWORD>"));
    assertTrue(result.contains("<password>****</password>"));
    // Note: The current implementation handles exact case matches for XML
  }

  @Test
  void testContentTypeVariations() {
    Map<String, String> headers = new HashMap<>();

    // Test with charset
    headers.put("Content-Type", "application/json; charset=UTF-8");
    assertTrue(sanitizer.shouldSanitize("application/json; charset=UTF-8"));

    // Test case insensitive
    headers.put("content-type", "APPLICATION/JSON");
    assertTrue(sanitizer.shouldSanitize("APPLICATION/JSON"));
  }
}
