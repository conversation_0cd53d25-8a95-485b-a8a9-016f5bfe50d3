/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DynamicSanitizationRuleManagerTest {

  private DynamicSanitizationRuleManager ruleManager;

  @BeforeEach
  void setUp() {
    // Clear any existing singleton instance
    DynamicSanitizationRuleManager.setInstance(null);
    ruleManager = new DynamicSanitizationRuleManager();
  }

  @AfterEach
  void tearDown() {
    if (ruleManager != null) {
      ruleManager.stop();
    }
    DynamicSanitizationRuleManager.setInstance(null);
  }

  @Test
  void testStartWithoutEndpoint() {
    // Test that start() method works when no endpoint is configured
    // This should trigger the log message we're looking for

    System.out.println("=== Testing DynamicSanitizationRuleManager.start() ===");

    // Call start() - this should print logs
    ruleManager.start();

    // Verify that the manager is still functional
    assertNotNull(ruleManager.getCurrentConfig());

    System.out.println("=== Test completed ===");
  }

  @Test
  void testSingletonPattern() {
    DynamicSanitizationRuleManager.setInstance(ruleManager);

    DynamicSanitizationRuleManager instance = DynamicSanitizationRuleManager.getInstance();
    assertSame(ruleManager, instance);
  }

  @Test
  void testGetCurrentConfig() {
    SanitizationConfig config = ruleManager.getCurrentConfig();
    assertNotNull(config);
  }

  @Test
  void testGetCurrentRules() {
    // Should return null or empty list when no rules are configured
    assertNull(ruleManager.getCurrentRules());
  }
}
