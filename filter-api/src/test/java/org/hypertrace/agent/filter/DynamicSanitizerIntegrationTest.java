/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Integration test for dynamic sanitization functionality. This test verifies that the
 * DynamicSensitiveDataSanitizer is properly registered and can be used for data sanitization.
 */
class DynamicSanitizerIntegrationTest {

  @BeforeEach
  void setUp() {
    // Reset the registry before each test
    SanitizerRegistry.reset();

    // Clear any existing singleton instance
    DynamicSanitizationRuleManager.setInstance(null);

    // Clear system properties
    System.clearProperty("ht.data.sanitization.enabled");
    System.clearProperty("ht.data.sanitization.markers.enabled");
    System.clearProperty("ht.sanitization.config.endpoint");
    System.clearProperty("ht.service.name");
  }

  @AfterEach
  void tearDown() {
    // Reset the registry after each test
    SanitizerRegistry.reset();

    // Clear any existing singleton instance
    DynamicSanitizationRuleManager.setInstance(null);

    // Clear system properties
    System.clearProperty("ht.data.sanitization.enabled");
    System.clearProperty("ht.data.sanitization.markers.enabled");
    System.clearProperty("ht.sanitization.config.endpoint");
    System.clearProperty("ht.service.name");
  }

  @Test
  void testDynamicSanitizerRegistration() {
    // Configure dynamic sanitization
    System.setProperty(
        "ht.sanitization.config.endpoint", "http://localhost:8081/api/sanitization/rules");
    System.setProperty("ht.service.name", "test-service");

    // Get sanitizer from registry
    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();

    // Verify it's the dynamic sanitizer
    assertNotNull(sanitizer);
    assertTrue(sanitizer instanceof DynamicSensitiveDataSanitizer);
  }

  @Test
  void testDynamicSanitizerFallback() {
    // Configure dynamic sanitization but don't start the rule manager
    System.setProperty(
        "ht.sanitization.config.endpoint", "http://localhost:8081/api/sanitization/rules");
    System.setProperty("ht.service.name", "test-service");

    // Get sanitizer from registry
    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();

    // Verify it's the dynamic sanitizer
    assertTrue(sanitizer instanceof DynamicSensitiveDataSanitizer);

    // Test sanitization with fallback behavior
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    String testJson =
        "{\"username\":\"john\",\"password\":\"secret123\",\"email\":\"<EMAIL>\"}";
    String sanitized = sanitizer.sanitize(testJson, headers);

    // Should fall back to default sanitizer behavior
    assertNotNull(sanitized);
    assertNotEquals(testJson, sanitized);
    assertTrue(sanitized.contains("****")); // Should contain masked values
  }

  @Test
  void testDefaultSanitizerWhenDynamicDisabled() {
    // Don't configure dynamic endpoint

    // Get sanitizer from registry
    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();

    // Verify it's the default sanitizer
    assertNotNull(sanitizer);
    assertTrue(sanitizer instanceof DefaultSensitiveDataSanitizer);
  }

  @Test
  void testSanitizerSingleton() {
    // Configure dynamic sanitization
    System.setProperty(
        "ht.sanitization.config.endpoint", "http://localhost:8081/api/sanitization/rules");

    // Get sanitizer multiple times
    SensitiveDataSanitizer sanitizer1 = SanitizerRegistry.getSanitizer();
    SensitiveDataSanitizer sanitizer2 = SanitizerRegistry.getSanitizer();

    // Should be the same instance
    assertSame(sanitizer1, sanitizer2);
  }

  @Test
  void testContentTypeSanitization() {
    // Configure dynamic sanitization
    System.setProperty(
        "ht.sanitization.config.endpoint", "http://localhost:8081/api/sanitization/rules");

    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();

    // Test different content types
    assertTrue(sanitizer.shouldSanitize("application/json"));
    assertTrue(sanitizer.shouldSanitize("application/xml"));
    assertTrue(sanitizer.shouldSanitize("application/x-www-form-urlencoded"));
    assertTrue(sanitizer.shouldSanitize(null)); // Should sanitize unknown types by default
  }

  @Test
  void testBasicSanitization() {
    // Configure dynamic sanitization
    System.setProperty(
        "ht.sanitization.config.endpoint", "http://localhost:8081/api/sanitization/rules");

    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();

    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    // Test JSON sanitization
    String testJson = "{\"username\":\"john\",\"password\":\"secret123\"}";
    String sanitized = sanitizer.sanitize(testJson, headers);

    assertNotNull(sanitized);
    // Should not contain the original password
    assertFalse(sanitized.contains("secret123"));
  }
}
