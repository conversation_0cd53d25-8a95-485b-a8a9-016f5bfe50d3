/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.Span;
import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.SanitizerRegistry;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class DataSanitizationUtilsTest {

  private Span mockSpan;
  private Map<String, String> headers;
  private static final AttributeKey<String> TEST_ATTRIBUTE =
      AttributeKey.stringKey("test.attribute");

  @BeforeEach
  void setUp() {
    mockSpan = mock(Span.class);
    headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
  }

  @AfterEach
  void tearDown() {
    System.clearProperty("ht.data.sanitization.enabled");
    SanitizerRegistry.reset();
  }

  @Test
  void testAddSanitizedAttribute_WithSensitiveData() {
    String body = "{\"password\":\"secret123\",\"username\":\"<EMAIL>\"}";

    DataSanitizationUtils.addSanitizedAttribute(mockSpan, TEST_ATTRIBUTE, body, headers);

    ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);
    verify(mockSpan).setAttribute(eq(TEST_ATTRIBUTE), valueCaptor.capture());

    String sanitizedValue = valueCaptor.getValue();
    assertTrue(sanitizedValue.contains("\"password\":\"****\""));
    assertTrue(sanitizedValue.contains("\"username\":\"****@example.com\""));
  }

  @Test
  void testAddSanitizedAttribute_NullSpan() {
    DataSanitizationUtils.addSanitizedAttribute(null, TEST_ATTRIBUTE, "test", headers);
    // Should not throw exception
  }

  @Test
  void testAddSanitizedAttribute_NullBody() {
    DataSanitizationUtils.addSanitizedAttribute(mockSpan, TEST_ATTRIBUTE, null, headers);
    verify(mockSpan, never()).setAttribute(any(AttributeKey.class), anyString());
  }

  @Test
  void testAddSanitizedAttribute_EmptyBody() {
    DataSanitizationUtils.addSanitizedAttribute(mockSpan, TEST_ATTRIBUTE, "", headers);
    verify(mockSpan, never()).setAttribute(any(AttributeKey.class), anyString());
  }

  @Test
  void testSanitize_WithSensitiveData() {
    String body = "{\"api_key\":\"sk_test_123456\",\"data\":\"normal\"}";

    String result = DataSanitizationUtils.sanitize(body, headers);

    assertNotNull(result);
    assertTrue(result.contains("\"api_key\":\"****\""));
    assertTrue(result.contains("\"data\":\"normal\""));
  }

  @Test
  void testSanitize_NullInput() {
    assertNull(DataSanitizationUtils.sanitize(null, headers));
  }

  @Test
  void testSanitize_EmptyInput() {
    assertEquals("", DataSanitizationUtils.sanitize("", headers));
  }

  @Test
  void testSanitize_DisabledSanitization() {
    System.setProperty("ht.data.sanitization.enabled", "false");

    String body = "{\"password\":\"secret123\"}";
    String result = DataSanitizationUtils.sanitize(body, headers);

    // Should return original when disabled
    assertEquals(body, result);
  }

  @Test
  void testSanitize_UnsupportedContentType() {
    Map<String, String> imageHeaders = new HashMap<>();
    imageHeaders.put("Content-Type", "image/jpeg");

    String body = "some binary data with password=secret";
    String result = DataSanitizationUtils.sanitize(body, imageHeaders);

    // Should return original for unsupported content type
    assertEquals(body, result);
  }

  @Test
  void testSanitize_NoHeaders() {
    String body = "email: <EMAIL>, password: secret";
    String result = DataSanitizationUtils.sanitize(body, null);

    // Should still sanitize when headers are null (defaults to plain text)
    assertNotNull(result);
    assertTrue(result.contains("****@example.com"));
  }

  @Test
  void testAddSanitizedAttribute_FormData() {
    Map<String, String> formHeaders = new HashMap<>();
    formHeaders.put("Content-Type", "application/x-www-form-urlencoded");

    String body = "username=admin&password=secret&token=abc123";

    DataSanitizationUtils.addSanitizedAttribute(mockSpan, TEST_ATTRIBUTE, body, formHeaders);

    ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);
    verify(mockSpan).setAttribute(eq(TEST_ATTRIBUTE), valueCaptor.capture());

    String sanitizedValue = valueCaptor.getValue();
    assertTrue(sanitizedValue.contains("password=****"));
    assertTrue(sanitizedValue.contains("token=****"));
    assertTrue(sanitizedValue.contains("username=admin"));
  }
}
