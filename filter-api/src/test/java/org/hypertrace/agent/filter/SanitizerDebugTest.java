/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class SanitizerDebugTest {

  @Test
  public void debugSanitization() {
    DefaultSensitiveDataSanitizer sanitizer = new DefaultSensitiveDataSanitizer(true);
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");

    // Test nested objects
    String input =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"auth\":{"
            + "\"token\":\"Bearer abc123xyz\","
            + "\"secret\":\"supersecret\""
            + "}"
            + "},"
            + "\"data\":\"regular data\""
            + "}";

    System.out.println("Input: " + input);
    String result = sanitizer.sanitize(input, headers);
    System.out.println("Result: " + result);

    // Check specific assertions
    System.out.println("\nChecking assertions:");
    System.out.println("Contains email: " + result.contains("\"email\":\"****@example.com\""));
    System.out.println("Contains token: " + result.contains("\"token\":\"****\""));
    System.out.println("Contains secret: " + result.contains("\"secret\":\"****\""));

    // Test case insensitive
    String input2 = "{" + "\"EMAIL\":\"<EMAIL>\"," + "\"Password\":\"secret123\"" + "}";

    System.out.println("\nInput2: " + input2);
    String result2 = sanitizer.sanitize(input2, headers);
    System.out.println("Result2: " + result2);
  }
}
