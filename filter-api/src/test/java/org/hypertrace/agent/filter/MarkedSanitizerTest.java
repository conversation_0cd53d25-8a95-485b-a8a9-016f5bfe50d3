/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MarkedSanitizerTest {

  private SensitiveDataSanitizer sanitizer;
  private Map<String, String> jsonHeaders;
  private Map<String, String> xmlHeaders;
  private Map<String, String> formHeaders;

  @BeforeEach
  void setUp() {
    // Create sanitizer with markers enabled
    sanitizer = new DefaultSensitiveDataSanitizer(true, true);

    // Setup headers for different content types
    jsonHeaders = new HashMap<>();
    jsonHeaders.put("Content-Type", "application/json");

    xmlHeaders = new HashMap<>();
    xmlHeaders.put("Content-Type", "application/xml");

    formHeaders = new HashMap<>();
    formHeaders.put("Content-Type", "application/x-www-form-urlencoded");
  }

  @AfterEach
  void tearDown() {
    // Clear system properties after each test
    System.clearProperty("ht.data.sanitization.enabled");
    System.clearProperty("ht.data.sanitization.markers.enabled");
  }

  @Test
  void testJsonSanitizationWithMarkers() {
    String json =
        "{"
            + "\"user\":{"
            + "\"email\":\"<EMAIL>\","
            + "\"password\":\"secret123\","
            + "\"creditCard\":\"****************\""
            + "}"
            + "}";

    String sanitized = sanitizer.sanitize(json, jsonHeaders);

    // Check that markers are present
    assertTrue(sanitized.contains("[SENSITIVE:EMAIL]"));
    assertTrue(sanitized.contains("[SENSITIVE:PASSWORD]"));
    assertTrue(sanitized.contains("@example.com"));
  }

  @Test
  void testXmlSanitizationWithMarkers() {
    String xml =
        "<user>"
            + "<email><EMAIL></email>"
            + "<password>secret123</password>"
            + "<creditCard>****************</creditCard>"
            + "</user>";

    String sanitized = sanitizer.sanitize(xml, xmlHeaders);

    // Check that markers are present
    assertTrue(sanitized.contains("[SENSITIVE:EMAIL]"));
    assertTrue(sanitized.contains("[SENSITIVE:PASSWORD]"));
    assertTrue(sanitized.contains("@example.com"));
  }

  @Test
  void testFormDataSanitizationWithMarkers() {
    String formData = "email=<EMAIL>&password=secret123&creditCard=****************";

    String sanitized = sanitizer.sanitize(formData, formHeaders);

    // Check that markers are present
    assertTrue(sanitized.contains("[SENSITIVE:EMAIL]"));
    assertTrue(sanitized.contains("[SENSITIVE:PASSWORD]"));
    assertTrue(sanitized.contains("@example.com"));
  }

  @Test
  void testPatternDetectionWithMarkers() {
    String text = "My <NAME_EMAIL> and my credit card is 4111-1111-1111-1111";

    String sanitized = sanitizer.sanitize(text, null);

    // Check that pattern markers are present
    assertTrue(sanitized.contains("[DETECTED:EMAIL]"));
    assertTrue(sanitized.contains("[DETECTED:CREDIT_CARD]"));
    assertTrue(sanitized.contains("@example.com"));
  }
}
