# Filter API Module

The `filter-api` module provides the core filtering and data sanitization capabilities for the Hypertrace Java Agent. It includes automatic sensitive data sanitization to prevent exposure of sensitive information in traces.

## Overview

This module offers:
- **Automatic Data Sanitization**: Built-in sanitization of sensitive data in request/response bodies
- **Filter Provider SPI**: Extensible architecture for custom filter implementations
- **Content-Type Aware Processing**: Different sanitization strategies for JSON, XML, form data, and plain text

## Automatic Data Sanitization

### Supported Sensitive Data Types

The built-in sanitizer automatically detects and masks the following types of sensitive data:

#### 1. Sensitive Field Names (case-insensitive):
- **Passwords**: `password`, `passwd`, `pwd`, `pass`
- **Keys/Tokens**: `secret`, `token`, `apikey`, `api_key`, `api-key`, `authorization`, `auth`
- **Payment Info**: `credit_card`, `creditcard`, `card_number`, `cardnumber`, `cvv`, `cvc`
- **Personal Info**: `ssn`, `social_security_number`, `email`, `phone`, `mobile`
- **Account Info**: `account_number`, `accountnumber`, `pin`, `passcode`

#### 2. Pattern-Based Detection:
- **Email Addresses**: Masked as `****@domain.com` (preserves domain for debugging)
- **Credit Card Numbers**: 13-19 digit numbers
- **Social Security Numbers**: XXX-XX-XXXX format or 9-digit numbers
- **Phone Numbers**: US format phone numbers
- **API Keys**: Alphanumeric strings of 20+ characters

### Supported Content Types

- `application/json`
- `application/xml`
- `text/xml`
- `application/x-www-form-urlencoded`
- `text/plain`

### Configuration

Data sanitization is **enabled by default**. You can control it via:

**System Properties:**
```bash
# Enable/disable data sanitization
java -Dht.data.sanitization.enabled=false -javaagent:hypertrace-agent.jar YourApp

# Enable detection markers (disabled by default)
java -Dht.data.sanitization.markers.enabled=true -javaagent:hypertrace-agent.jar YourApp
```

**Environment Variables:**
```bash
export HT_DATA_SANITIZATION_ENABLED=false
export HT_DATA_SANITIZATION_MARKERS_ENABLED=true
```

### Detection Markers

When markers are enabled (`ht.data.sanitization.markers.enabled=true`), the sanitizer will add labels to indicate what type of sensitive data was detected:

- **Field-based detection**: `[SENSITIVE:TYPE]****`
- **Pattern-based detection**: `[DETECTED:TYPE]****`

Supported marker types:
- `PASSWORD` - Password fields
- `TOKEN` - API keys, tokens, secrets
- `EMAIL` - Email addresses
- `PAYMENT` - Credit card, CVV fields
- `SSN` - Social Security Numbers
- `PHONE` - Phone numbers
- `ACCOUNT` - Account numbers, PINs
- `CREDIT_CARD` - Credit card patterns
- `API_KEY` - API key patterns

## Creating Custom Filters

### 1. Implement FilterProvider

Create a class implementing the `FilterProvider` interface:

```java
package com.example.filters;

import org.hypertrace.agent.filter.spi.FilterProvider;
import org.hypertrace.agent.filter.api.Filter;
import org.hypertrace.agent.filter.spi.FilterProviderConfig;
import com.google.auto.service.AutoService;

@AutoService(FilterProvider.class)
public class CustomFilterProvider implements FilterProvider {

    @Override
    public Filter create(FilterProviderConfig config) {
        // Access configuration if needed
        String customParam = config.getConfig("my.custom.param", "default");

        return new CustomFilter(customParam);
    }
}
```

### 2. Implement Filter

Create your filter implementation:

```java
package com.example.filters;

import org.hypertrace.agent.filter.api.Filter;
import org.hypertrace.agent.core.filter.FilterResult;
import io.opentelemetry.api.trace.Span;
import java.util.Map;

public class CustomFilter implements Filter {

    private final String customParam;

    public CustomFilter(String customParam) {
        this.customParam = customParam;
    }

    @Override
    public FilterResult evaluateRequestHeaders(Span span, Map<String, String> headers) {
        // Implement custom header evaluation logic
        // Return FilterResult(shouldBlock, statusCode, blockingReason)

        if (headers.containsKey("X-Forbidden")) {
            return new FilterResult(true, 403, "Forbidden header detected");
        }

        return new FilterResult(false, 0, null);
    }

    @Override
    public FilterResult evaluateRequestBody(Span span, String body, Map<String, String> headers) {
        // Implement custom body evaluation logic
        // You can also perform custom sanitization here

        if (body != null && body.contains("malicious")) {
            return new FilterResult(true, 400, "Malicious content detected");
        }

        return new FilterResult(false, 0, null);
    }
}
```

### 3. Register with Service Loader

Create a file `META-INF/services/org.hypertrace.agent.filter.spi.FilterProvider` in your resources:

```
com.example.filters.CustomFilterProvider
```

### 4. Package and Deploy

Include your custom filter JAR in the classpath when running the agent:

```bash
java -cp custom-filters.jar -javaagent:hypertrace-agent.jar YourApp
```

## Custom Data Sanitization

To implement custom sanitization logic:

### 1. Create a Custom Sanitizer

```java
package com.example.sanitizers;

import org.hypertrace.agent.filter.api.Filter;
import org.hypertrace.agent.core.filter.FilterResult;
import io.opentelemetry.api.trace.Span;
import java.util.Map;
import java.util.regex.Pattern;

public class CustomSanitizationFilter implements Filter {

    private static final Pattern INTERNAL_ID_PATTERN = Pattern.compile("emp\\d{6}");

    @Override
    public FilterResult evaluateRequestHeaders(Span span, Map<String, String> headers) {
        // No header sanitization in this example
        return new FilterResult(false, 0, null);
    }

    @Override
    public FilterResult evaluateRequestBody(Span span, String body, Map<String, String> headers) {
        if (body != null && headers.containsKey("Content-Type")) {
            String contentType = headers.get("Content-Type");

            if (contentType.contains("application/json")) {
                // Custom sanitization for employee IDs
                String sanitized = INTERNAL_ID_PATTERN.matcher(body).replaceAll("emp****");

                // Add sanitized body to span
                span.setAttribute("http.request.body", sanitized);
            }
        }

        return new FilterResult(false, 0, null);
    }
}
```

### 2. Control Filter Execution Order

Filters are executed in the order they are discovered. To control execution order:

1. Use priority in your filter provider name (filters are sorted alphabetically)
2. Disable specific providers via configuration:
   ```bash
   -Dht.filter.provider.com.example.UnwantedFilterProvider.disabled=true
   ```

## Using in Instrumentation

When developing custom instrumentation, use the `DataSanitizationUtils`:

```java
import org.hypertrace.agent.filter.utils.DataSanitizationUtils;

// In your instrumentation code
public void captureRequestData(Span span, String body, Map<String, String> headers) {
    // This will automatically apply sanitization if enabled
    DataSanitizationUtils.addSanitizedAttribute(
        span,
        "http.request.body",
        body,
        headers
    );
}
```

## Performance Considerations

1. **Sanitization Overhead**: Processing large JSON/XML documents adds latency
2. **Memory Usage**: Sanitization may create temporary copies of data
3. **Regex Performance**: Complex patterns can be CPU-intensive

Best practices:
- Enable sanitization only for sensitive endpoints
- Use content-type filtering to limit processing
- Consider sampling for high-traffic services

## Troubleshooting

### Sanitization Not Working

1. Check if sanitization is enabled:
   ```bash
   # Should not have this property set to false
   -Dht.data.sanitization.enabled=false
   ```

2. Verify content type is supported
3. Check agent logs for sanitization errors
4. Ensure your custom filter is being loaded (check for service loader registration)

### Custom Filter Not Loading

1. Verify `META-INF/services` file is correct
2. Check classpath includes your filter JAR
3. Look for initialization errors in agent logs
4. Ensure `@AutoService` annotation is processed (if using)

## Security Best Practices

1. **Defense in Depth**: Don't rely solely on sanitization—avoid logging sensitive data
2. **Regular Updates**: Keep sanitization patterns up-to-date
3. **Testing**: Regularly test sanitization with production-like data
4. **Monitoring**: Alert on sanitization failures or bypasses
5. **Encryption**: Use HTTPS/TLS for trace data transmission

## Examples

### Example: Sanitized JSON

**Original:**
```json
{
  "username": "<EMAIL>",
  "password": "secretPass123",
  "creditCard": "****************",
  "apiKey": "sk_live_abcdef123456789012345678"
}
```

**After Sanitization (without markers):**
```json
{
  "username": "****@example.com",
  "password": "****",
  "creditCard": "****",
  "apiKey": "****"
}
```

**After Sanitization (with markers enabled):**
```json
{
  "username": "[SENSITIVE:EMAIL]****@example.com",
  "password": "[SENSITIVE:PASSWORD]****",
  "creditCard": "[SENSITIVE:PAYMENT]****",
  "apiKey": "[SENSITIVE:TOKEN]****"
}
```

### Example: Custom Filter Configuration

```yaml
# In your config file
filter:
  providers:
    - class: com.example.CustomFilterProvider
      config:
        my.custom.param: "value"
        sanitize.internal.ids: true
```

## API Reference

See the JavaDoc for detailed API documentation:
- `FilterProvider` - SPI for creating filters
- `Filter` - Core filter interface
- `FilterResult` - Result of filter evaluation
- `DataSanitizationUtils` - Utilities for instrumentation
