# 动态脱敏规则 API 接口规范

## 概述

Hypertrace Java Agent 支持通过 HTTP API 接口动态下发和更新敏感数据脱敏规则。这允许集中管理和实时更新脱敏策略，无需重启应用程序。

## 配置方式

### 系统属性配置

```bash
java -Dht.sanitization.config.endpoint=http://config-server:8080/api/sanitization/rules \
     -Dht.sanitization.config.auth.token=your-auth-token \
     -Dht.sanitization.config.refresh.interval=300 \
     -Dht.service.name=your-service-name \
     -javaagent:hypertrace-agent.jar YourApp
```

### 环境变量配置

```bash
export HT_SANITIZATION_CONFIG_ENDPOINT=http://config-server:8080/api/sanitization/rules
export HT_SANITIZATION_CONFIG_AUTH_TOKEN=your-auth-token
export HT_SANITIZATION_CONFIG_REFRESH_INTERVAL=300
export HT_SERVICE_NAME=your-service-name
```

## API 接口规范

### 获取脱敏规则配置

**请求**:
```
GET /api/sanitization/rules
Authorization: Bearer your-auth-token
X-Service-Name: your-service-name
Content-Type: application/json
```

**响应**:
```json
{
  "version": "1.0.0",
  "timestamp": 1703123456789,
  "enabled": true,
  "markersEnabled": true,
  "markerFormat": "BRACKET",
  "globalSettings": {
    "defaultMaskValue": "****",
    "maxRuleCount": 100
  },
  "rules": [
    {
      "id": "password-fields",
      "name": "Password Field Detection",
      "description": "Detect and sanitize password-related fields",
      "type": "FIELD_NAME",
      "severity": "HIGH",
      "enabled": true,
      "priority": 10,
      "fieldNames": ["password", "passwd", "pwd"],
      "maskValue": "****",
      "markerType": "PASSWORD",
      "preserveFormat": false,
      "contentTypes": ["application/json", "application/xml"]
    }
  ]
}
```

## 脱敏规则数据结构

### SanitizationConfig

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| version | String | 是 | 配置版本号 |
| timestamp | Long | 是 | 配置时间戳 |
| enabled | Boolean | 是 | 全局启用/禁用脱敏 |
| markersEnabled | Boolean | 否 | 是否启用检测标记 |
| markerFormat | String | 否 | 标记格式 (BRACKET/PREFIX/XML) |
| globalSettings | Object | 否 | 全局设置 |
| rules | Array | 是 | 脱敏规则列表 |

### SanitizationRule

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | String | 是 | 规则唯一标识 |
| name | String | 是 | 规则名称 |
| description | String | 否 | 规则描述 |
| type | Enum | 是 | 规则类型 (FIELD_NAME/PATTERN/CONTENT_TYPE/CUSTOM) |
| severity | Enum | 否 | 严重性级别 (LOW/MEDIUM/HIGH/CRITICAL) |
| enabled | Boolean | 是 | 规则是否启用 |
| priority | Integer | 否 | 优先级 (数字越小优先级越高) |
| fieldNames | Array | 否 | 字段名称列表 (FIELD_NAME 类型使用) |
| pattern | String | 否 | 正则表达式模式 (PATTERN 类型使用) |
| contentTypes | Array | 否 | 适用的内容类型 |
| maskValue | String | 否 | 脱敏掩码值 |
| markerType | String | 否 | 标记类型 |
| preserveFormat | Boolean | 否 | 是否保留格式 |
| includeServices | Array | 否 | 仅对指定服务生效 |
| excludeServices | Array | 否 | 排除的服务 |

## 规则类型说明

### 1. FIELD_NAME - 基于字段名称

检测结构化数据中的敏感字段名称：

```json
{
  "id": "email-fields",
  "type": "FIELD_NAME",
  "fieldNames": ["email", "mail", "emailAddress"],
  "markerType": "EMAIL",
  "preserveFormat": true
}
```

### 2. PATTERN - 基于正则表达式模式

使用正则表达式检测敏感数据模式：

```json
{
  "id": "credit-card-pattern",
  "type": "PATTERN",
  "pattern": "\\b(?:\\d[ -]*?){13,19}\\b",
  "markerType": "CREDIT_CARD"
}
```

### 3. CONTENT_TYPE - 基于内容类型

针对特定内容类型的脱敏规则：

```json
{
  "id": "json-sensitive-data",
  "type": "CONTENT_TYPE",
  "contentTypes": ["application/json"],
  "fieldNames": ["sensitiveData"]
}
```

## 配置示例

### 基础密码字段脱敏

```json
{
  "id": "password-basic",
  "name": "Basic Password Fields",
  "type": "FIELD_NAME",
  "severity": "HIGH",
  "enabled": true,
  "priority": 10,
  "fieldNames": ["password", "passwd", "pwd", "pass"],
  "maskValue": "****",
  "markerType": "PASSWORD",
  "contentTypes": ["application/json", "application/xml"]
}
```

### 邮箱地址脱敏（保留域名）

```json
{
  "id": "email-preserve-domain",
  "name": "Email with Domain Preservation",
  "type": "FIELD_NAME",
  "severity": "MEDIUM",
  "enabled": true,
  "priority": 20,
  "fieldNames": ["email", "mail"],
  "maskValue": "****",
  "markerType": "EMAIL",
  "preserveFormat": true,
  "contentTypes": ["application/json"]
}
```

### 信用卡号模式检测

```json
{
  "id": "credit-card-regex",
  "name": "Credit Card Pattern",
  "type": "PATTERN",
  "severity": "HIGH",
  "enabled": true,
  "priority": 5,
  "pattern": "\\b(?:\\d[ -]*?){13,19}\\b",
  "maskValue": "****",
  "markerType": "CREDIT_CARD",
  "contentTypes": ["application/json", "text/plain"]
}
```

### 服务特定规则

```json
{
  "id": "hr-employee-id",
  "name": "HR Service Employee ID",
  "type": "PATTERN",
  "severity": "LOW",
  "enabled": true,
  "priority": 30,
  "pattern": "\\bemp\\d{6}\\b",
  "maskValue": "emp******",
  "markerType": "INTERNAL_ID",
  "includeServices": ["hr-service", "payroll-service"]
}
```

## 最佳实践

### 1. 规则优先级设计

- **1-10**: 关键敏感数据 (密码、API密钥、信用卡)
- **11-20**: 高敏感数据 (SSN、个人身份信息)
- **21-30**: 中等敏感数据 (邮箱、电话)
- **31-50**: 低敏感数据 (内部ID、业务代码)

### 2. 性能优化

- 限制规则数量 (建议不超过100个)
- 使用高效的正则表达式
- 合理设置刷新间隔 (建议5-10分钟)
- 启用模式缓存

### 3. 安全考虑

- 使用 HTTPS 传输配置
- 实施适当的身份验证和授权
- 记录配置变更审计日志
- 定期审查和更新规则

### 4. 监控和告警

- 监控配置加载成功率
- 监控脱敏规则执行性能
- 设置配置更新失败告警
- 跟踪敏感数据检测统计

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查网络连接和端点可用性
   - 验证认证令牌是否有效
   - 查看 agent 日志中的错误信息

2. **规则不生效**
   - 确认规则的 `enabled` 字段为 `true`
   - 检查 `contentTypes` 是否匹配
   - 验证 `includeServices`/`excludeServices` 配置

3. **性能问题**
   - 减少复杂的正则表达式规则
   - 降低配置刷新频率
   - 限制规则总数

### 调试模式

启用调试日志：

```bash
java -Dlogging.level.org.hypertrace.agent.filter=DEBUG \
     -javaagent:hypertrace-agent.jar YourApp
```

## 集成示例

### Spring Boot 配置服务

```java
@RestController
@RequestMapping("/api/sanitization")
public class SanitizationConfigController {
    
    @GetMapping("/rules")
    public SanitizationConfig getRules(
            @RequestHeader("X-Service-Name") String serviceName) {
        // 根据服务名称返回相应的脱敏规则
        return sanitizationConfigService.getConfigForService(serviceName);
    }
}
```

这个动态脱敏规则系统提供了灵活、可扩展的敏感数据保护机制，支持实时更新和集中管理，大大提高了数据安全治理的效率和效果。
