/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.core.instrumentation.utils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ContentTypeCharsetUtils {
  private ContentTypeCharsetUtils() {}

  private static final Logger log = LoggerFactory.getLogger(ContentTypeCharsetUtils.class);

  // System property to override default charset
  private static final String DEFAULT_CHARSET_PROPERTY = "hypertrace.agent.default.charset";

  // default for HTTP 1.1 https://www.w3.org/International/articles/http-charset/index
  // Changed to UTF-8 for better Chinese character support in Spring Boot applications
  private static final Charset DEFAULT_CHARSET = getConfiguredDefaultCharset();

  /**
   * Get the configured default charset from system property or fallback to UTF-8. Users can
   * override by setting -Dhypertrace.agent.default.charset=ISO-8859-1
   */
  private static Charset getConfiguredDefaultCharset() {
    String charsetName = System.getProperty(DEFAULT_CHARSET_PROPERTY);
    if (charsetName != null && !charsetName.isEmpty()) {
      try {
        return Charset.forName(charsetName);
      } catch (Exception e) {
        log.warn(
            "Invalid charset specified in system property {}: {}, falling back to UTF-8",
            DEFAULT_CHARSET_PROPERTY,
            charsetName);
      }
    }
    return StandardCharsets.UTF_8;
  }

  public static Charset toCharset(String charsetName) {
    if (charsetName == null || charsetName.isEmpty()) {
      return DEFAULT_CHARSET;
    }
    try {
      return Charset.forName(charsetName);
    } catch (Exception e) {
      log.error(
          "Could not parse encoding {} to charset, using default {}", charsetName, DEFAULT_CHARSET);
    }
    return DEFAULT_CHARSET;
  }

  /**
   * Get charset with Spring Boot application context awareness. For Spring Boot applications,
   * prefer UTF-8 over ISO-8859-1 when no charset is specified.
   */
  public static Charset toCharsetWithSpringBootSupport(String charsetName, String contentType) {
    if (charsetName == null || charsetName.isEmpty()) {
      // For JSON content types, prefer UTF-8 as it's the de facto standard
      if (contentType != null
          && (contentType.toLowerCase().contains("json")
              || contentType.toLowerCase().contains("application/json"))) {
        return StandardCharsets.UTF_8;
      }
      return DEFAULT_CHARSET;
    }
    try {
      return Charset.forName(charsetName);
    } catch (Exception e) {
      log.error(
          "Could not parse encoding {} to charset, using default {}", charsetName, DEFAULT_CHARSET);
    }
    return DEFAULT_CHARSET;
  }

  public static Charset getDefaultCharset() {
    return DEFAULT_CHARSET;
  }
}
