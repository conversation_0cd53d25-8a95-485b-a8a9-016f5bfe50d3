/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.core.instrumentation.buffer;

import static org.hypertrace.agent.core.instrumentation.utils.ContentTypeUtils.convertToJSONString;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.Span;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;

/** Created to represent the request body that is in x-www-form-urlencoded format. */
public class StringMapSpanPair {

  public final Span span;
  public final Map<String, String> headers;
  public final Map<String, String> stringMap;
  private final BiFunction<String, Map<String, String>, String> sanitizer;

  /** A flag to signalize that map has been added to span. */
  private boolean mapCaptured;

  public StringMapSpanPair(Span span, Map<String, String> stringMap, Map<String, String> headers) {
    this(span, stringMap, headers, (body, hdrs) -> body); // Default no-op sanitizer
  }

  public StringMapSpanPair(
      Span span,
      Map<String, String> stringMap,
      Map<String, String> headers,
      BiFunction<String, Map<String, String>, String> sanitizer) {
    this.span = span;
    this.stringMap = stringMap;
    this.headers = headers;
    this.sanitizer = Objects.requireNonNull(sanitizer);
  }

  /**
   * Return a JSON string representing the contents of the x-www-form-urlencoded body.
   *
   * @param attributeKey
   */
  public void captureBody(AttributeKey<String> attributeKey) {
    if (!mapCaptured) {
      String json = convertToJSONString(stringMap);
      span.setAttribute(attributeKey, sanitizer.apply(json, headers));
      mapCaptured = true;
    }
  }
}
