/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.core.instrumentation.buffer;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.Span;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import org.hypertrace.agent.core.TriFunction;
import org.hypertrace.agent.core.filter.FilterResult;
import org.hypertrace.agent.core.instrumentation.HypertraceEvaluationException;

public class ByteBufferSpanPair {

  public final Span span;
  private final BoundedByteArrayOutputStream buffer;
  private final Map<String, String> headers;
  private boolean bufferCaptured;
  private final TriFunction<Span, String, Map<String, String>, FilterResult> filter;
  private final BiFunction<String, Map<String, String>, String> sanitizer;

  public ByteBufferSpanPair(
      Span span,
      BoundedByteArrayOutputStream buffer,
      TriFunction<Span, String, Map<String, String>, FilterResult> filter,
      Map<String, String> headers) {
    this(span, buffer, filter, headers, (body, hdrs) -> body); // Default no-op sanitizer
  }

  public ByteBufferSpanPair(
      Span span,
      BoundedByteArrayOutputStream buffer,
      TriFunction<Span, String, Map<String, String>, FilterResult> filter,
      Map<String, String> headers,
      BiFunction<String, Map<String, String>, String> sanitizer) {
    this.span = span;
    this.buffer = buffer;
    this.filter = Objects.requireNonNull(filter);
    this.headers = headers;
    this.sanitizer = Objects.requireNonNull(sanitizer);
  }

  public void captureBody(AttributeKey<String> attributeKey) {
    if (bufferCaptured) {
      return;
    }
    bufferCaptured = true;

    String requestBody = null;
    try {
      requestBody = buffer.toStringWithSuppliedCharset();
    } catch (UnsupportedEncodingException e) {
      // ignore charset has been parsed before
    }
    span.setAttribute(attributeKey, sanitizer.apply(requestBody, headers));
    final FilterResult filterResult;
    filterResult = filter.apply(span, requestBody, headers);
    if (filterResult.shouldBlock()) {
      throw new HypertraceEvaluationException(filterResult);
    }
  }

  public void writeToBuffer(byte singleByte) {
    bufferCaptured = false;
    buffer.write(singleByte);
  }

  public void writeToBuffer(byte[] b, int offset, int len) {
    bufferCaptured = false;
    buffer.write(b, offset, len);
  }

  public void writeToBuffer(byte[] b) throws IOException {
    bufferCaptured = false;
    buffer.write(b);
  }
}
