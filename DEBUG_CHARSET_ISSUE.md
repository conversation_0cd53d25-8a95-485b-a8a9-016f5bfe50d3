# Hypertrace Java Agent 字符编码问题调试

## 🔍 当前状态

用户报告即使在之前的修复后，<PERSON><PERSON><PERSON> 中存储的 `http.response.body` 仍然出现中文乱码：

```json
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}
```

## 🛠️ 已实施的修复

### 1. Content-Encoding 响应头修复 ✅
- 移除了错误的 Content-Encoding 设置
- 修复了概念混淆问题

### 2. ServletOutputStream 路径字符编码修复 ✅
- 在响应完成时重新检查字符编码
- 使用正确的字符编码解析字节数据

### 3. 默认字符编码改进 ✅
- 默认字符编码改为 UTF-8
- 添加 Spring Boot 支持

## 🔍 当前调试方案

### 添加的调试信息

在 `Utils.java` 中添加了详细的调试日志：

#### ServletOutputStream 路径
```java
System.out.println("DEBUG: ServletOutputStream path - charset: " + currentCharsetStr + 
                  " -> " + currentCharset + ", contentType: " + contentType + 
                  ", responseBody length: " + responseBody.length());
if (responseBody.length() > 0) {
  System.out.println("DEBUG: First 100 chars: " + 
                    (responseBody.length() > 100 ? responseBody.substring(0, 100) : responseBody));
}
```

#### PrintWriter 路径
```java
System.out.println("DEBUG: PrintWriter path - charset: " + currentCharsetStr + 
                  ", contentType: " + contentType + 
                  ", responseBody length: " + responseBody.length());
if (responseBody.length() > 0) {
  System.out.println("DEBUG: First 100 chars: " + 
                    (responseBody.length() > 100 ? responseBody.substring(0, 100) : responseBody));
}
```

## 🎯 需要收集的信息

请运行修改后的 Agent 并提供以下信息：

### 1. 控制台调试输出
- 查找以 "DEBUG:" 开头的日志行
- 确定使用的是哪个路径（ServletOutputStream 还是 PrintWriter）
- 记录字符编码和内容类型信息

### 2. 响应体内容检查
- 查看调试输出中的 "First 100 chars" 
- 确认在 Agent 捕获时内容是否已经乱码

### 3. Spring Boot 配置
- 检查应用的字符编码配置
- 确认 Content-Type 设置

## 🔍 可能的问题原因

### 1. 时序问题
- Spring Boot 可能在更晚的时机设置字符编码
- Agent 捕获时机可能仍然过早

### 2. 多层编码问题
- 数据可能经过了多次编码转换
- 需要确认原始数据的编码状态

### 3. 其他代码路径
- 可能存在其他未修复的代码路径
- 需要确认实际使用的代码路径

### 4. PrintWriter 特殊处理
- PrintWriter 处理字符数据，不是字节数据
- 可能需要不同的修复方案

## 📋 下一步行动计划

### 基于调试输出的分析

1. **如果使用 ServletOutputStream 路径**：
   - 检查字符编码是否正确检测
   - 验证字节到字符串的转换是否正确

2. **如果使用 PrintWriter 路径**：
   - 检查字符数据是否在捕获时就已经乱码
   - 可能需要不同的修复策略

3. **如果调试输出显示内容正确**：
   - 问题可能在数据传输到 Jaeger 的过程中
   - 需要检查 OpenTelemetry 的序列化过程

### 可能的进一步修复

1. **更深层的字符编码处理**
2. **PrintWriter 路径的特殊处理**
3. **数据传输过程的编码保护**

## 🚀 测试指令

```bash
# 使用修改后的 Agent 运行应用
java -javaagent:hypertrace-agent.jar -jar your-spring-boot-app.jar

# 查看控制台输出中的 DEBUG 信息
# 访问返回中文数据的 API
# 检查 Jaeger 中的数据
```

请提供调试输出，这将帮助我们准确定位问题并实施正确的修复方案。
