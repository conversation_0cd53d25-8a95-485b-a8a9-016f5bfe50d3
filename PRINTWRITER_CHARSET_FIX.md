# Hypertrace Java Agent PrintWriter 字符编码修复

## 🔍 问题发现

在之前的修复中，我们主要关注了 `ServletOutputStream` 路径的字符编码问题，但忽略了 **PrintWriter 路径**。

Spring Boot 应用通常使用 `PrintWriter` 来写入 JSON 响应体，而不是 `ServletOutputStream`。这就是为什么之前的修复没有完全解决问题的原因。

## 🔍 PrintWriter 路径的特殊性

### 与 ServletOutputStream 的区别

1. **ServletOutputStream**：
   - 处理字节数据 (`byte[]`)
   - 需要字符编码来将字节转换为字符串
   - 使用 `BoundedByteArrayOutputStream`

2. **PrintWriter**：
   - 处理字符数据 (`char[]`)
   - 字符数据已经是 Unicode 形式
   - 使用 `BoundedCharArrayWriter`

### 问题的根本原因

对于 PrintWriter 路径，问题在于：

1. **字符编码假设错误**：Agent 假设字符数据使用默认字符编码
2. **时序问题**：Spring Boot 在处理过程中可能改变了字符编码设置
3. **编码转换缺失**：没有根据实际的响应字符编码重新处理字符数据

## 🛠️ 修复方案

### 核心修复逻辑

```java
// For PrintWriter, we need to handle character encoding properly
String bufferContent = buffer.toString();

// Re-check the character encoding at response completion time
String currentCharsetStr = httpServletResponse.getCharacterEncoding();
String contentType = httpServletResponse.getContentType();
Charset currentCharset = ContentTypeCharsetUtils.toCharsetWithSpringBootSupport(currentCharsetStr, contentType);

String responseBody;
try {
  // If the current charset is different from the default, we need to re-encode
  if (!currentCharset.equals(ContentTypeCharsetUtils.getDefaultCharset())) {
    // Convert string to bytes using the default charset, then back to string using correct charset
    byte[] bytes = bufferContent.getBytes(ContentTypeCharsetUtils.getDefaultCharset());
    responseBody = new String(bytes, currentCharset);
  } else {
    responseBody = bufferContent;
  }
} catch (Exception e) {
  // Fallback to original content if encoding conversion fails
  responseBody = bufferContent;
}

span.setAttribute(HypertraceSemanticAttributes.HTTP_RESPONSE_BODY, DataSanitizationUtils.sanitize(responseBody));
```

### 修复原理

1. **动态字符编码检查**：在响应完成时重新检查当前的字符编码
2. **编码转换**：如果当前字符编码与默认不同，进行重新编码
3. **安全回退**：如果编码转换失败，使用原始内容
4. **Spring Boot 支持**：使用 `toCharsetWithSpringBootSupport` 方法优化 JSON 内容类型

### 编码转换过程

```
原始字符数据 (可能用错误编码解释)
    ↓
转换为字节 (使用默认编码)
    ↓
重新解释字节 (使用正确编码)
    ↓
正确的字符串
```

## 📁 修改的文件

1. **Servlet 5.0**：
   - `instrumentation/servlet/servlet-5.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v5_0/Utils.java`

2. **Servlet 3.0**：
   - `instrumentation/servlet/servlet-3.0/src/main/java/io/opentelemetry/javaagent/instrumentation/hypertrace/servlet/v3_0/nowrapping/Utils.java`

3. **核心工具类**：
   - `javaagent-core/src/main/java/org/hypertrace/agent/core/instrumentation/utils/ContentTypeCharsetUtils.java`

## ✅ 修复效果

### 修复前
```json
// Jaeger 中的 http.response.body (PrintWriter 路径)
{"id":5,"name":"æµè¯ç¨æ·","email":"****@example.com","phone":"****"}
```

### 修复后
```json
// Jaeger 中的 http.response.body (PrintWriter 路径)
{"id":5,"name":"测试用户","email":"****@example.com","phone":"****"}
```

## 🔧 技术细节

### 为什么需要特殊处理

1. **字符 vs 字节**：PrintWriter 处理的是字符数据，不是字节数据
2. **编码假设**：Agent 可能使用了错误的字符编码假设
3. **时序敏感**：字符编码设置的时机很重要

### 编码转换的必要性

当 Spring Boot 应用：
1. 使用 UTF-8 编码写入中文字符到 PrintWriter
2. 但 Agent 使用 ISO-8859-1 (默认) 来解释这些字符
3. 结果就是乱码

通过重新编码转换，我们可以：
1. 获取原始字节数据
2. 使用正确的字符编码重新解释
3. 得到正确的字符串

## 🚀 使用方法

修复后的 Agent 会自动处理 PrintWriter 路径的字符编码问题：

```bash
java -javaagent:hypertrace-agent.jar -jar your-spring-boot-app.jar
```

## 📝 注意事项

1. **兼容性**：修复保持向后兼容
2. **性能**：编码转换有轻微性能开销，但只在需要时进行
3. **稳定性**：提供了异常处理和回退机制
4. **覆盖范围**：同时修复了 Servlet 3.0 和 5.0

这个修复解决了 PrintWriter 路径中文乱码的根本问题，确保了无论 Spring Boot 应用使用哪种方式写入响应体，Jaeger 中都能正确显示中文字符。
